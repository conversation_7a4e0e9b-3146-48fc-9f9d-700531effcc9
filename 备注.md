https://api2.buyvs.com/apps
<EMAIL>
NNqHh4hRQhuC2Y6zL5ZB


https://supabase.com/
Project name: ai homework helper
Database Password: zoWBgHtc4gsqxWIe

https://resend.com/
API Key: re_MjW5ANhi_BHz4yntGVoomrTEF4xbu7e6J

# 启动所有服务
pnpm run dev

# 在 apps\web 目录下启动 可以响应运行日志
\apps\web> npm run dev

注意在 apps\web 目录下执行命令
# 当前数据库的迁移状态：

pnpm --filter web supabase migration list

# 命令行方式删除特定的迁移记录 20250803000000
pnpm supabase migration repair --status reverted 20250803000000

# 检查迁移状态
pnpm supabase migration list

# 推送新的迁移
pnpm supabase db push --include-all

# Supabase 类型生成
数据库结构变更后，需要重新生成 TypeScript 类型
没有安装Docker，数据库实际是远程连接，所以需要直接修改类型文件。