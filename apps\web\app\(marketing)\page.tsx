import Link from 'next/link';

import {
  MessageSquare,
  BookOpen,
  Video,
  FileText,
  Zap,
  Users,
  Clock,
  Star
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { ChatSection } from './_components/chat-section';
import { withI18n } from '~/lib/i18n/with-i18n';

function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <div className="container mx-auto px-4 py-8">
        <ChatSection />
      </div>

      {/* Study Tools Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            <Trans i18nKey="marketing:studyToolsTitle" defaults="Looking for more study tools?" />
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Smart Notes */}
          <Card className="bg-gradient-to-br from-primary/10 to-primary/20 border-primary/20">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-foreground">
                <Trans i18nKey="marketing:smartNotesTitle" defaults="Smart Notes for Better Learning" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-background rounded-lg p-4 min-h-[120px] flex items-center justify-center">
                <BookOpen className="w-12 h-12 text-primary" />
              </div>
              <Button variant="outline" className="w-full">
                <Trans i18nKey="marketing:generateNote" defaults="Generate note" />
              </Button>
            </CardContent>
          </Card>

          {/* File Summary */}
          <Card className="bg-gradient-to-br from-green-500/10 to-green-500/20 border-green-500/20">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-foreground">
                <Trans i18nKey="marketing:fileSummaryTitle" defaults="Get a Quick Summary of Your File" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-foreground">
                  <FileText className="w-4 h-4" />
                  <span><Trans i18nKey="marketing:pdfSummarizer" defaults="PDF Summarizer" /></span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-foreground">
                  <FileText className="w-4 h-4" />
                  <span><Trans i18nKey="marketing:pptSummarizer" defaults="PPT Summarizer" /></span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-foreground">
                  <FileText className="w-4 h-4" />
                  <span><Trans i18nKey="marketing:docSummarizer" defaults="Doc Summarizer" /></span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-foreground">
                  <BookOpen className="w-4 h-4" />
                  <span><Trans i18nKey="marketing:bookSummarizer" defaults="Book Summarizer" /></span>
                </div>
              </div>
              <Link href="#" className="text-primary text-sm hover:underline">
                <Trans i18nKey="marketing:seeAll" defaults="See all" />
              </Link>
            </CardContent>
          </Card>

          {/* AI Lecture Note Taker */}
          <Card className="bg-gradient-to-br from-pink-500/10 to-pink-500/20 border-pink-500/20">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-foreground">
                <Trans i18nKey="marketing:lectureNoteTakerTitle" defaults="AI Lecture Note Taker" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-background rounded-lg p-4 min-h-[120px] flex flex-col items-center justify-center">
                <MessageSquare className="w-12 h-12 text-pink-500 mb-2" />
                <p className="text-sm text-muted-foreground text-center">
                  <Trans
                    i18nKey="marketing:lectureNoteTakerDesc"
                    defaults="Record your lecture, and let AI turn it into notes."
                  />
                </p>
              </div>
              <Button variant="outline" className="w-full">
                <Trans i18nKey="marketing:startRecording" defaults="Start recording" />
              </Button>
            </CardContent>
          </Card>

          {/* AI YouTube Video Summarizer */}
          <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/20 border-blue-500/20">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-foreground">
                <Trans i18nKey="marketing:videoSummarizerTitle" defaults="AI YouTube Video Summarizer" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-background rounded-lg p-4 min-h-[120px] flex flex-col items-center justify-center">
                <Video className="w-12 h-12 text-blue-500 mb-2" />
                <p className="text-sm text-muted-foreground text-center">
                  <Trans
                    i18nKey="marketing:videoSummarizerDesc"
                    defaults="No need to watch the whole video, let AI summarize it for you."
                  />
                </p>
              </div>
              <Button variant="outline" className="w-full">
                <Trans i18nKey="marketing:tryItNow" defaults="Try it now" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Why Use Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-foreground mb-4">
            <Trans i18nKey="marketing:whyUseTitle" defaults="Why use StudyX's homework helper?" />
          </h2>
          <p className="text-lg text-muted-foreground max-w-4xl mx-auto">
            <Trans
              i18nKey="marketing:whyUseDescription"
              defaults="Whether you're tackling tough assignments or preparing for exams, StudyX's powerful AI homework helper helps you achieve better."
            />
          </p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <Card className="text-center bg-gradient-to-br from-blue-500/10 to-blue-500/20 border-blue-500/20">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">98%</h3>
              <p className="text-muted-foreground font-medium">
                <Trans i18nKey="marketing:solutionAccuracy" defaults="Solution Accuracy" />
              </p>
            </CardContent>
          </Card>

          <Card className="text-center bg-gradient-to-br from-pink-500/10 to-pink-500/20 border-pink-500/20">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-pink-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">85M+</h3>
              <p className="text-muted-foreground font-medium">
                <Trans i18nKey="marketing:communityAnswers" defaults="Community Answers" />
              </p>
            </CardContent>
          </Card>

          <Card className="text-center bg-gradient-to-br from-primary/10 to-primary/20 border-primary/20">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-primary-foreground" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">24/7</h3>
              <p className="text-muted-foreground font-medium">
                <Trans i18nKey="marketing:homeworkHelp" defaults="Homework Help" />
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Study Smarter Section */}
        <div className="bg-gradient-to-r from-primary to-secondary rounded-2xl p-8 md:p-12 text-primary-foreground text-center">
          <h3 className="text-3xl font-bold mb-4">
            <Trans i18nKey="marketing:studySmarterTitle" defaults="Study Smarter Anytime, Anywhere!" />
          </h3>
          <p className="text-lg mb-8 opacity-90">
            <Trans
              i18nKey="marketing:studySmarterDescription"
              defaults="Access StudyX seamlessly on your laptop, phone, or tablet, and get instant homework help on all your favorite platforms—making it easier, faster, and more convenient to learn."
            />
          </p>
          <div className="flex items-center justify-center space-x-4 mb-8">
            <Star className="w-6 h-6 text-yellow-400 fill-current" />
            <span className="text-lg font-semibold">
              <Trans i18nKey="marketing:rating" defaults="4.9/5 rating" />
            </span>
            <span className="text-lg opacity-75">
              <Trans i18nKey="marketing:lovedBy" defaults="Loved by 10+ million students worldwide" />
            </span>
          </div>
          <Button size="lg" variant="secondary">
            <Trans i18nKey="marketing:askQuestion" defaults="Ask your question" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default withI18n(Home);
