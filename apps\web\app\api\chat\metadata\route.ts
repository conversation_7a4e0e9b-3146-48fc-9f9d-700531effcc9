import { NextRequest, NextResponse } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { ConversationService } from '~/lib/services/conversation-service';
import { MetadataStatus } from '~/lib/types/conversations';

/**
 * 元数据处理 API 端点
 * POST /api/chat/metadata
 * 
 * 用于异步处理会话元数据：
 * 1. 调用 Dify 元数据工作流
 * 2. 解析结构化输出
 * 3. 更新数据库记录
 */

interface MetadataRequest {
  conversation_id: string;  // Dify 对话ID
  short_id: string;         // 系统短ID
  query: string;
  answer?: string;
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body: MetadataRequest = await request.json();
    const { conversation_id, short_id, query, answer } = body;

    // 验证必需参数
    if (!conversation_id || !short_id || !query) {
      return NextResponse.json(
        { error: 'conversation_id, short_id and query are required' },
        { status: 400 }
      );
    }

    console.log('=== Metadata Processing Started ===');
    console.log('Conversation ID:', conversation_id);
    console.log('Short ID:', short_id);
    console.log('Query length:', query.length);
    console.log('Answer length:', answer?.length || 0);

    // 获取服务实例
    const difyServices = getDifyServices();
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 更新状态为处理中
    await conversationService.updateMetadata({
      conversation_id: short_id,  // 使用 short_id 更新数据库
      title: '',
      description: '',
      handle: '',
      language: '',
      category_id: 1,
      metadata_status: MetadataStatus.PROCESSING,
    });

    try {
      // 调用 Dify 元数据工作流
      console.log('Calling Dify metadata workflow...');
      const metadataResponse = await difyServices.workflow.generateMetadata(
        conversation_id,  // Dify 对话ID
        short_id,         // 系统短ID
        query,            // 用户查询
        answer            // AI回答
      );

      console.log('Metadata response received:', metadataResponse);

      // 验证工作流响应结构
      if (!metadataResponse.data || !metadataResponse.data.outputs) {
        throw new Error('Invalid workflow response: missing data.outputs');
      }

      const { structured_output, short_id: returnedShortId } = metadataResponse.data.outputs;
      console.log('Structured output:', structured_output);
      console.log('Returned short_id:', returnedShortId);

      if (!structured_output || !structured_output.title) {
        throw new Error('Invalid structured output from metadata workflow');
      }

      // 验证返回的 short_id 与传入的一致
      if (returnedShortId && returnedShortId !== short_id) {
        console.warn(`Short ID mismatch: expected ${short_id}, got ${returnedShortId}`);
      }

      // 解析分类
      let categoryId = 1; // 默认分类
      if (structured_output.category) {
        const categoryStr = structured_output.category.toString().toLowerCase();
        // 简单的分类映射
        const categoryMap: Record<string, number> = {
          'math': 1,
          'science': 2,
          'english': 3,
          'history': 4,
          'programming': 5,
          'other': 6
        };
        categoryId = categoryMap[categoryStr] || 1;
      }

      // 更新数据库记录（使用 short_id）
      const updateResult = await conversationService.updateMetadata({
        conversation_id: short_id,  // 使用 short_id 作为数据库查询键
        title: structured_output.title,
        description: structured_output.description,
        handle: structured_output.handle,
        language: structured_output.language || 'zh',
        category_id: categoryId,
        metadata_status: MetadataStatus.COMPLETED,
      });

      if (!updateResult.success) {
        throw new Error(`Failed to update metadata: ${updateResult.error}`);
      }

      console.log('Metadata processing completed successfully');

      return NextResponse.json({
        success: true,
        data: {
          conversation_id: short_id,  // 返回 short_id
          metadata_status: MetadataStatus.COMPLETED,
          structured_output,
          updated_record: updateResult.data
        }
      });

    } catch (metadataError) {
      console.error('Metadata processing failed:', metadataError);

      // 更新状态为失败
      await conversationService.updateMetadata({
        conversation_id: short_id,  // 使用 short_id
        title: '',
        description: '',
        handle: '',
        language: '',
        category_id: 1,
        metadata_status: MetadataStatus.FAILED,
      });

      return NextResponse.json({
        success: false,
        error: 'Metadata processing failed',
        details: metadataError instanceof Error ? metadataError.message : 'Unknown error',
        metadata_status: MetadataStatus.FAILED
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Metadata API error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * 获取元数据处理状态
 * GET /api/chat/metadata?conversation_id=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversation_id');

    if (!conversationId) {
      return NextResponse.json(
        { error: 'conversation_id parameter is required' },
        { status: 400 }
      );
    }

    // 获取数据库记录
    const supabase = getSupabaseServerAdminClient();
    const { data, error } = await supabase
      .from('conversations_index')
      .select('metadata_status, title, description, handle, language, category_id')
      .eq('short_id', conversationId)
      .single();

    if (error) {
      console.error('Failed to get metadata status:', error);
      return NextResponse.json(
        { error: 'Failed to get metadata status' },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        conversation_id: conversationId,
        metadata_status: data.metadata_status,
        metadata: {
          title: data.title,
          description: data.description,
          handle: data.handle,
          language: data.language,
          category_id: data.category_id
        }
      }
    });

  } catch (error) {
    console.error('Get metadata status error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * 重试元数据处理
 * PUT /api/chat/metadata
 */
export async function PUT(request: NextRequest) {
  try {
    const body: MetadataRequest = await request.json();
    const { conversation_id, query, answer } = body;

    if (!conversation_id || !query) {
      return NextResponse.json(
        { error: 'conversation_id and query are required' },
        { status: 400 }
      );
    }

    console.log('=== Metadata Retry Started ===');
    console.log('Conversation ID:', conversation_id);

    // 重置状态为等待处理
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    await conversationService.updateMetadata({
      conversation_id,
      title: '',
      description: '',
      handle: '',
      language: '',
      category_id: 1,
      metadata_status: MetadataStatus.PENDING,
    });

    // 调用 POST 方法重新处理
    return POST(request);

  } catch (error) {
    console.error('Metadata retry error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
