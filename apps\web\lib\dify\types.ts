/**
 * Dify API 相关的类型定义
 */

export interface ChatRequest {
  query: string;
  conversationId?: string;
  files?: FileAttachment[];
  inputs?: Record<string, any>;
}

export interface FileAttachment {
  id?: string;
  type: 'image' | 'document';
  name: string;
  size: number;
  mimeType: string;
  url?: string;
  uploadFileId?: string;
}

export interface ChatResponse {
  id: string;
  answer: string;
  conversationId: string;
  createdAt: number;
  
  // Dify 结构化输出
  structured_output?: {
    title: string;
    description: string;
    handle: string;
    language: string;
    category: number;  // 学科分类 ID
  };
  
  metadata?: {
    usage?: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
  };
}

export interface DifyMessage {
  id: string;
  conversation_id: string;
  inputs: Record<string, any>;
  query?: string;  // 用户输入
  answer?: string; // AI 回复
  message_files?: Array<{
    id: string;
    type: string;
    url: string;
    belongs_to: string;
  }>;
  feedback?: {
    rating: 'like' | 'dislike' | null;
  };
  retriever_resources?: Array<{
    position: number;
    dataset_id: string;
    dataset_name: string;
    document_id: string;
    document_name: string;
    data_source_type: string;
    segment_id: string;
    score: number;
    content: string;
  }>;
  created_at: number;
  agent_thoughts?: Array<{
    id: string;
    chain_id: string;
    message_id: string;
    position: number;
    thought: string;
    tool: string;
    tool_input: string;
    created_at: number;
  }>;
  message_metadata?: {
    annotation_reply?: {
      id: string;
      account_id: string;
      content: string;
      created_at: number;
    };
    usage?: {
      prompt_tokens: number;
      prompt_unit_price: string;
      prompt_price_unit: string;
      prompt_price: string;
      completion_tokens: number;
      completion_unit_price: string;
      completion_price_unit: string;
      completion_price: string;
      total_tokens: number;
      total_price: string;
      currency: string;
      latency: number;
    };
  };
}

export interface StreamChunk {
  event: 'message' | 'message_end' | 'error';
  data: any;
}

export interface UploadFileRequest {
  file: File;
  userId: string;
}

export interface UploadFileResponse {
  id: string;
  name: string;
  size: number;
  extension: string;
  mimeType: string;
  createdBy: string;
  createdAt: number;
}

/**
 * API 错误类型
 */
export class DifyApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string,
  ) {
    super(message);
    this.name = 'DifyApiError';
  }
}

/**
 * 支持的文件类型
 */
export const SUPPORTED_FILE_TYPES = {
  images: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
  ],
  documents: [
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ],
} as const;

/**
 * 文件大小限制（字节）
 */
export const FILE_SIZE_LIMITS = {
  image: 10 * 1024 * 1024, // 10MB
  document: 50 * 1024 * 1024, // 50MB
} as const;

/**
 * 验证文件类型
 */
export function isValidFileType(file: File): boolean {
  const allSupportedTypes = [
    ...SUPPORTED_FILE_TYPES.images,
    ...SUPPORTED_FILE_TYPES.documents,
  ] as readonly string[];
  return allSupportedTypes.includes(file.type);
}

/**
 * 获取文件类型分类
 */
export function getFileCategory(file: File): 'image' | 'document' | 'unsupported' {
  if (SUPPORTED_FILE_TYPES.images.includes(file.type as any)) {
    return 'image';
  }
  if (SUPPORTED_FILE_TYPES.documents.includes(file.type as any)) {
    return 'document';
  }
  return 'unsupported';
}

/**
 * 验证文件大小
 */
export function isValidFileSize(file: File): boolean {
  const category = getFileCategory(file);
  if (category === 'unsupported') return false;
  
  const limit = category === 'image' ? FILE_SIZE_LIMITS.image : FILE_SIZE_LIMITS.document;
  return file.size <= limit;
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 生成用户 ID（临时方案，后续集成真实用户系统）
 */
export function generateUserId(): string {
  const timestamp = Date.now();
  const randomPart = Math.random().toString(36).substring(2, 11);
  const userId = `user_${timestamp}_${randomPart}`;

  console.log('=== generateUserId Debug ===');
  console.log('timestamp:', timestamp);
  console.log('randomPart:', randomPart);
  console.log('generated userId:', userId);
  console.log('============================');

  return userId;
}
