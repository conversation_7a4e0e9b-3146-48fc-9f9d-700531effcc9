/**
 * 会话服务类 - 处理会话相关的数据库操作
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../database.types';
import {
  ConversationIndexRow,
  ConversationIndexInsert,
  ConversationIndexUpdate,
  ConversationQueryParams,
  ConversationListResponse,
  SaveConversationRequest,
  SaveConversationResponse,
  CreateConversationRequest,
  UpdateMetadataRequest,
  UserType,
  MetadataStatus,
  ClientType,
} from '../types/conversations';
import { generateUniqueShortId, isValidShortId } from '../utils/short-id';

export class ConversationService {
  constructor(private supabase: SupabaseClient<Database>) {}

  /**
   * 创建会话记录（新架构）
   */
  async createConversation(request: CreateConversationRequest): Promise<SaveConversationResponse> {
    try {
      const {
        short_id,
        user_id = null,
        user_type = UserType.GUEST,
        is_public = true,
        metadata_status = MetadataStatus.PENDING,
        client_type = ClientType.WEB,
        ai_model = 'gpt-4.1-mini',
      } = request;

      // 准备插入数据
      const insertData: ConversationIndexInsert = {
        short_id,
        user_id,
        is_public,
        user_type,
        metadata_status: metadata_status as string,
        client_type: client_type as string,
        ai_model,
        view_count: 0,
        like_count: 0,
      };

      // 插入新的对话索引记录
      const { data, error } = await this.supabase
        .from('conversations_index')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create conversation: ${error.message}`);
      }

      return {
        success: true,
        data,
        message: 'Conversation created successfully',
      };
    } catch (error) {
      console.error('ConversationService.createConversation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 更新会话的 conversation_id 和 conversation_user
   */
  async updateConversationId(shortId: string, conversationId: string, conversationUser?: string): Promise<SaveConversationResponse> {
    try {
      const updateData: ConversationIndexUpdate = {
        conversation_id: conversationId,
        conversation_user: conversationUser,
        metadata_status: MetadataStatus.PROCESSING as string,
      };

      const { data, error } = await this.supabase
        .from('conversations_index')
        .update(updateData)
        .eq('short_id', shortId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update conversation ID: ${error.message}`);
      }

      return {
        success: true,
        data,
        message: 'Conversation ID updated successfully',
      };
    } catch (error) {
      console.error('ConversationService.updateConversationId error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 更新会话元数据
   */
  async updateMetadata(request: UpdateMetadataRequest): Promise<SaveConversationResponse> {
    try {
      const {
        conversation_id,
        title,
        description,
        handle,
        language,
        category_id,
        metadata_status,
      } = request;

      const updateData: ConversationIndexUpdate = {
        title,
        description,
        handle,
        language,
        category_id,
        metadata_status: metadata_status as string,
      };

      const { data, error } = await this.supabase
        .from('conversations_index')
        .update(updateData)
        .eq('short_id', conversation_id)  // conversation_id 实际上是 short_id
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update metadata: ${error.message}`);
      }

      return {
        success: true,
        data,
        message: 'Metadata updated successfully',
      };
    } catch (error) {
      console.error('ConversationService.updateMetadata error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 保存会话索引（兼容旧架构）
   */
  async saveConversation(request: SaveConversationRequest): Promise<SaveConversationResponse> {
    try {
      const {
        conversation_id,
        structured_output,
        user_id = null,
        user_type = UserType.GUEST,
        is_public = true, // 默认为 true 用于测试
        conversation_user,
        client_type = 'web',
        ai_model = 'gpt-4.1-mini',
      } = request;

      const { title, description, handle, language, category } = structured_output;

      // 生成唯一的短链接 ID
      const short_id = await generateUniqueShortId(async (id: string) => {
        const { data } = await this.supabase
          .from('conversations_index')
          .select('id')
          .eq('short_id', id)
          .maybeSingle();
        return !data; // 返回 true 表示唯一（不存在）
      });

      // 检查对话是否已存在
      const { data: existing, error: checkError } = await this.supabase
        .from('conversations_index')
        .select('id')
        .eq('conversation_id', conversation_id)
        .maybeSingle();

      if (checkError) {
        console.error('Database check error details:', {
          code: checkError.code,
          message: checkError.message,
          details: checkError.details,
          hint: checkError.hint,
        });
        throw new Error(`Database check error: ${checkError.message}`);
      }

      // 如果对话已存在，返回现有记录
      if (existing) {
        const { data: existingRecord, error: fetchError } = await this.supabase
          .from('conversations_index')
          .select('*')
          .eq('conversation_id', conversation_id)
          .single();

        if (fetchError) {
          throw new Error(`Failed to fetch existing conversation: ${fetchError.message}`);
        }

        return {
          success: true,
          data: existingRecord,
          message: 'Conversation already exists',
        };
      }

      console.log('=== ConversationService Debug ===');
      console.log('Received conversation_user:', conversation_user);
      console.log('conversation_user type:', typeof conversation_user);
      console.log('conversation_user is undefined:', conversation_user === undefined);
      console.log('conversation_user is null:', conversation_user === null);
      console.log('conversation_user is empty string:', conversation_user === '');

      // 如果没有提供 conversation_user，生成一个默认值
      const finalConversationUser = conversation_user || `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      console.log('Final conversation_user to save:', finalConversationUser);

      // 准备插入数据
      const insertData: ConversationIndexInsert = {
        conversation_id,
        short_id,
        handle,
        title,
        description,
        language,
        category_id: category,
        user_id,
        is_public,
        user_type,
        metadata_status: 'completed', // 旧架构直接设为完成
        client_type: client_type as string,
        ai_model,
        view_count: 0,
        like_count: 0,
        conversation_user: finalConversationUser,
      };

      console.log('Insert data conversation_user:', insertData.conversation_user);
      console.log('==================================');

      // 插入新的对话索引记录
      const { data, error } = await this.supabase
        .from('conversations_index')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to save conversation: ${error.message}`);
      }

      return {
        success: true,
        data,
        message: 'Conversation saved successfully',
      };
    } catch (error) {
      console.error('ConversationService.saveConversation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 根据 conversation_id 获取会话
   */
  async getConversationByConversationId(conversationId: string): Promise<ConversationIndexRow | null> {
    try {
      const { data, error } = await this.supabase
        .from('conversations_index')
        .select('*')
        .eq('conversation_id', conversationId)
        .maybeSingle();

      if (error) {
        throw new Error(`Failed to fetch conversation: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('ConversationService.getConversationByConversationId error:', error);
      return null;
    }
  }

  /**
   * 根据短 ID 获取会话
   */
  async getConversationByShortId(shortId: string): Promise<ConversationIndexRow | null> {
    try {
      if (!isValidShortId(shortId)) {
        throw new Error('Invalid short ID format');
      }

      const { data, error } = await this.supabase
        .from('conversations_index')
        .select('*')
        .eq('short_id', shortId)
        .maybeSingle();

      if (error) {
        throw new Error(`Failed to fetch conversation: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('ConversationService.getConversationByShortId error:', error);
      return null;
    }
  }

  /**
   * 获取会话列表
   */
  async getConversations(params: ConversationQueryParams = {}): Promise<ConversationListResponse> {
    try {
      const {
        page = 1,
        limit = 20,
        category_id,
        language,
        is_public,
        user_id,
        search,
        sort_by = 'created_at',
        sort_order = 'desc',
      } = params;

      let query = this.supabase.from('conversations_index').select('*', { count: 'exact' });

      // 应用筛选条件
      if (category_id !== undefined) {
        query = query.eq('category_id', category_id);
      }

      if (language !== undefined) {
        query = query.eq('language', language);
      }

      if (is_public !== undefined) {
        query = query.eq('is_public', is_public);
      }

      if (user_id !== undefined) {
        query = query.eq('user_id', user_id);
      }

      if (search) {
        query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
      }

      // 应用排序
      query = query.order(sort_by, { ascending: sort_order === 'asc' });

      // 应用分页
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Failed to fetch conversations: ${error.message}`);
      }

      const totalPages = count ? Math.ceil(count / limit) : 0;

      return {
        success: true,
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          total_pages: totalPages,
        },
      };
    } catch (error) {
      console.error('ConversationService.getConversations error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 更新会话信息
   */
  async updateConversation(
    shortId: string,
    updates: ConversationIndexUpdate
  ): Promise<ConversationIndexRow | null> {
    try {
      if (!isValidShortId(shortId)) {
        throw new Error('Invalid short ID format');
      }

      const { data, error } = await this.supabase
        .from('conversations_index')
        .update(updates)
        .eq('short_id', shortId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update conversation: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('ConversationService.updateConversation error:', error);
      return null;
    }
  }

  /**
   * 增加浏览量
   */
  async incrementViewCount(shortId: string): Promise<boolean> {
    try {
      if (!isValidShortId(shortId)) {
        throw new Error('Invalid short ID format');
      }

      // 先获取当前记录
      const { data: current, error: fetchError } = await this.supabase
        .from('conversations_index')
        .select('view_count')
        .eq('short_id', shortId)
        .single();

      if (fetchError || !current) {
        throw new Error(`Failed to fetch current view count: ${fetchError?.message || 'Record not found'}`);
      }

      // 更新浏览量
      const { error } = await this.supabase
        .from('conversations_index')
        .update({ view_count: current.view_count + 1 })
        .eq('short_id', shortId);

      if (error) {
        throw new Error(`Failed to increment view count: ${error.message}`);
      }

      return true;
    } catch (error) {
      console.error('ConversationService.incrementViewCount error:', error);
      return false;
    }
  }

  /**
   * 增加点赞数
   */
  async incrementLikeCount(shortId: string): Promise<boolean> {
    try {
      if (!isValidShortId(shortId)) {
        throw new Error('Invalid short ID format');
      }

      // 先获取当前记录
      const { data: current, error: fetchError } = await this.supabase
        .from('conversations_index')
        .select('like_count')
        .eq('short_id', shortId)
        .single();

      if (fetchError || !current) {
        throw new Error(`Failed to fetch current like count: ${fetchError?.message || 'Record not found'}`);
      }

      // 更新点赞数
      const { error } = await this.supabase
        .from('conversations_index')
        .update({ like_count: current.like_count + 1 })
        .eq('short_id', shortId);

      if (error) {
        throw new Error(`Failed to increment like count: ${error.message}`);
      }

      return true;
    } catch (error) {
      console.error('ConversationService.incrementLikeCount error:', error);
      return false;
    }
  }

  /**
   * 删除会话（软删除，设置为私有）
   */
  async deleteConversation(shortId: string): Promise<boolean> {
    try {
      if (!isValidShortId(shortId)) {
        throw new Error('Invalid short ID format');
      }

      const { error } = await this.supabase
        .from('conversations_index')
        .update({ is_public: false })
        .eq('short_id', shortId);

      if (error) {
        throw new Error(`Failed to delete conversation: ${error.message}`);
      }

      return true;
    } catch (error) {
      console.error('ConversationService.deleteConversation error:', error);
      return false;
    }
  }
}
