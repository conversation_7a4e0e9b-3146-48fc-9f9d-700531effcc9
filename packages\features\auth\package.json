{"name": "@kit/auth", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./sign-in": "./src/sign-in.ts", "./sign-up": "./src/sign-up.ts", "./password-reset": "./src/password-reset.ts", "./shared": "./src/shared.ts", "./mfa": "./src/mfa.ts", "./captcha/client": "./src/captcha/client/index.ts", "./captcha/server": "./src/captcha/server/index.ts", "./resend-email-link": "./src/components/resend-auth-link-form.tsx", "./oauth-provider-logo-image": "./src/components/oauth-provider-logo-image.tsx"}, "devDependencies": {"@hookform/resolvers": "^5.1.1", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "2.52.0", "@tanstack/react-query": "5.83.0", "@types/react": "19.1.8", "lucide-react": "^0.525.0", "next": "15.4.3", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.1", "sonner": "^2.0.6", "zod": "^3.25.74"}, "prettier": "@kit/prettier-config", "typesVersions": {"*": {"*": ["src/*"]}}}