/**
 * 会话相关的类型定义
 */

import { Database } from '../database.types';

// 从数据库类型中提取会话索引表的类型
export type ConversationIndexRow = Database['public']['Tables']['conversations_index']['Row'];
export type ConversationIndexInsert = Database['public']['Tables']['conversations_index']['Insert'];
export type ConversationIndexUpdate = Database['public']['Tables']['conversations_index']['Update'];

/**
 * 学科分类枚举
 */
export enum SubjectCategory {
  MATH = 1,
  PHYSICS = 2,
  CHEMISTRY = 3,
  BIOLOGY = 4,
  HISTORY = 5,
  CHINESE = 6,
  ENGLISH = 7,
  GEOGRAPHY = 8,
  POLITICS = 9,
  COMPUTER = 10,
  OTHER = 99,
}

/**
 * 学科分类映射
 */
export const SUBJECT_CATEGORY_MAP: Record<SubjectCategory, string> = {
  [SubjectCategory.MATH]: '数学',
  [SubjectCategory.PHYSICS]: '物理',
  [SubjectCategory.CHEMISTRY]: '化学',
  [SubjectCategory.BIOLOGY]: '生物',
  [SubjectCategory.HISTORY]: '历史',
  [SubjectCategory.CHINESE]: '语文',
  [SubjectCategory.ENGLISH]: '英语',
  [SubjectCategory.GEOGRAPHY]: '地理',
  [SubjectCategory.POLITICS]: '政治',
  [SubjectCategory.COMPUTER]: '计算机',
  [SubjectCategory.OTHER]: '其他',
};

/**
 * 用户类型枚举
 */
export enum UserType {
  GUEST = 0,
  REGISTERED = 1,
}

/**
 * 元数据处理状态枚举
 */
export enum MetadataStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

/**
 * 客户端类型枚举
 */
export enum ClientType {
  WEB = 'web',
  ANDROID = 'android',
  IOS = 'ios',
  DESKTOP = 'desktop',
  EXTENSION = 'extension',
  API = 'api',
}

/**
 * 元数据状态映射
 */
export const METADATA_STATUS_MAP: Record<MetadataStatus, string> = {
  [MetadataStatus.PENDING]: '等待处理',
  [MetadataStatus.PROCESSING]: '处理中',
  [MetadataStatus.COMPLETED]: '已完成',
  [MetadataStatus.FAILED]: '处理失败',
};

/**
 * 客户端类型映射
 */
export const CLIENT_TYPE_MAP: Record<ClientType, string> = {
  [ClientType.WEB]: '网页版',
  [ClientType.ANDROID]: '安卓APP',
  [ClientType.IOS]: '苹果APP',
  [ClientType.DESKTOP]: '桌面应用',
  [ClientType.EXTENSION]: '浏览器插件',
  [ClientType.API]: 'API调用',
};

/**
 * 会话创建请求参数（新架构）
 */
export interface CreateConversationRequest {
  short_id: string;
  user_id?: string | null;
  user_type?: UserType;
  is_public?: boolean;
  metadata_status?: MetadataStatus;
  client_type?: ClientType;
  ai_model?: string;
}

/**
 * 会话保存请求参数（兼容旧架构）
 */
export interface SaveConversationRequest {
  conversation_id: string;
  structured_output: {
    title: string;
    description: string;
    handle: string;
    language: string;
    category: number;
  };
  user_id?: string | null;
  user_type?: UserType;
  is_public?: boolean;
  conversation_user?: string; // Dify API 用户标识符
  client_type?: ClientType;
  ai_model?: string;
}

/**
 * 元数据更新请求参数
 */
export interface UpdateMetadataRequest {
  conversation_id: string;
  title: string;
  description: string;
  handle: string;
  language: string;
  category_id: number;
  metadata_status: MetadataStatus;
}

/**
 * 会话保存响应
 */
export interface SaveConversationResponse {
  success: boolean;
  data?: ConversationIndexRow;
  message?: string;
  error?: string;
  details?: string;
}

/**
 * 会话查询参数
 */
export interface ConversationQueryParams {
  page?: number;
  limit?: number;
  category_id?: number;
  language?: string;
  is_public?: boolean;
  user_id?: string;
  search?: string;
  sort_by?: 'created_at' | 'view_count' | 'like_count';
  sort_order?: 'asc' | 'desc';
}

/**
 * 会话列表响应
 */
export interface ConversationListResponse {
  success: boolean;
  data?: ConversationIndexRow[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
  error?: string;
}

/**
 * 会话详情响应（包含 Dify API 数据）
 */
export interface ConversationDetailResponse {
  success: boolean;
  data?: {
    // 数据库中的索引信息
    index: ConversationIndexRow;
    // 从 Dify API 获取的对话内容
    messages?: DifyConversationMessage[];
    // 统计信息
    stats?: {
      message_count: number;
      first_message_at: string;
      last_message_at: string;
    };
  };
  error?: string;
}

/**
 * Dify 对话消息类型
 */
export interface DifyConversationMessage {
  id: string;
  conversation_id: string;
  inputs: Record<string, any>;
  query: string;
  answer: string;
  message_files?: any[];
  feedback?: any;
  retriever_resources?: any[];
  created_at: number;
  agent_thoughts?: any[];
  message_metadata?: {
    annotation_reply?: any;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      prompt_unit_price: string;
      prompt_price_unit: string;
      prompt_price: string;
      completion_unit_price: string;
      completion_price_unit: string;
      completion_price: string;
      total_price: string;
      currency: string;
      latency: number;
    };
  };
}

/**
 * 会话更新请求参数
 */
export interface UpdateConversationRequest {
  conversation_id?: string;
  conversation_user?: string;
  view_count?: number;
  like_count?: number;
  is_public?: boolean;
  title?: string;
  description?: string;
  handle?: string;
  language?: string;
  category_id?: number;
  metadata_status?: MetadataStatus;
  client_type?: ClientType;
  ai_model?: string;
}

/**
 * API 错误响应
 */
export interface ApiErrorResponse {
  success: false;
  error: string;
  details?: string;
  code?: string;
}

/**
 * 验证会话保存请求参数
 */
export function validateSaveConversationRequest(data: any): data is SaveConversationRequest {
  return (
    typeof data === 'object' &&
    typeof data.conversation_id === 'string' &&
    data.conversation_id.length > 0 &&
    typeof data.structured_output === 'object' &&
    typeof data.structured_output.title === 'string' &&
    typeof data.structured_output.description === 'string' &&
    typeof data.structured_output.handle === 'string' &&
    typeof data.structured_output.language === 'string' &&
    typeof data.structured_output.category === 'number' &&
    data.structured_output.category >= 1 &&
    data.structured_output.category <= 99
  );
}

/**
 * 验证会话查询参数
 */
export function validateConversationQueryParams(params: any): ConversationQueryParams {
  const validated: ConversationQueryParams = {};

  if (params.page && !isNaN(parseInt(params.page))) {
    validated.page = Math.max(1, parseInt(params.page));
  }

  if (params.limit && !isNaN(parseInt(params.limit))) {
    validated.limit = Math.min(100, Math.max(1, parseInt(params.limit)));
  }

  if (params.category_id && !isNaN(parseInt(params.category_id))) {
    validated.category_id = parseInt(params.category_id);
  }

  if (params.language && typeof params.language === 'string') {
    validated.language = params.language;
  }

  if (params.is_public !== undefined) {
    validated.is_public = params.is_public === 'true' || params.is_public === true;
  }

  if (params.user_id && typeof params.user_id === 'string') {
    validated.user_id = params.user_id;
  }

  if (params.search && typeof params.search === 'string') {
    validated.search = params.search.trim();
  }

  if (params.sort_by && ['created_at', 'view_count', 'like_count'].includes(params.sort_by)) {
    validated.sort_by = params.sort_by;
  }

  if (params.sort_order && ['asc', 'desc'].includes(params.sort_order)) {
    validated.sort_order = params.sort_order;
  }

  return validated;
}

/**
 * 生成会话 URL
 */
export function generateConversationUrl(shortId: string, handle: string): string {
  return `/questions/${shortId}/${handle}`;
}

/**
 * 从 URL 中提取短 ID
 */
export function extractShortIdFromUrl(url: string): string | null {
  const match = url.match(/\/questions\/([^\/]+)/);
  return match?.[1] || null;
}
