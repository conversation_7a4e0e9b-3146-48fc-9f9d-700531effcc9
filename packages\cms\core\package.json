{"name": "@kit/cms", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/cms-types": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/keystatic": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/wordpress": "workspace:*", "@types/node": "^24.0.15"}, "typesVersions": {"*": {"*": ["src/*"]}}}