/**
 * 前端 Dify 客户端服务
 * 通过 API 路由调用 Dify 服务，获取对话内容
 */

import { DifyMessage } from '../dify/types';

/**
 * Dify 对话详情响应
 */
export interface DifyConversationDetail {
  id: string;
  conversation_id: string;
  messages: DifyMessage[];
  created_at: number;
  updated_at: number;
  message_count: number;
  // 可选字段
  name?: string;
  status?: string;
}

/**
 * API 响应类型
 */
type DifyApiResponse<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
  details?: string;
};

/**
 * 前端 Dify 客户端类
 */
export class DifyClient {
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  /**
   * 发送 HTTP 请求的通用方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<DifyApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}/api${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || 'Request failed',
          details: responseData.details,
        };
      }

      // 后端API已经返回了 { success: true, data: ... } 格式
      // 直接返回后端的响应，避免双重包装
      if (responseData.success) {
        return {
          success: true,
          data: responseData.data,
        };
      } else {
        return {
          success: false,
          error: responseData.error || 'Request failed',
          details: responseData.details,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * 获取对话消息列表
   */
  async getConversationMessages(conversationId: string): Promise<DifyApiResponse<DifyMessage[]>> {
    return this.request<DifyMessage[]>(`/dify/conversations/${conversationId}/messages`);
  }

  /**
   * 获取对话详情（包含消息）
   */
  async getConversationDetail(conversationId: string): Promise<DifyApiResponse<DifyConversationDetail>> {
    return this.request<DifyConversationDetail>(`/dify/conversations/${conversationId}`);
  }

  /**
   * 发送消息到对话
   */
  async sendMessage(params: {
    conversationId?: string;
    message: string;
    files?: Array<{
      type: 'image' | 'document';
      transfer_method: 'remote_url' | 'local_file';
      url?: string;
      upload_file_id?: string;
    }>;
  }): Promise<DifyApiResponse<DifyMessage>> {
    return this.request<DifyMessage>('/dify/chat', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  /**
   * 上传文件
   */
  async uploadFile(file: File): Promise<DifyApiResponse<{ id: string; name: string }>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<{ id: string; name: string }>('/dify/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // 让浏览器自动设置 Content-Type for FormData
    });
  }
}

/**
 * 默认的 Dify 客户端实例
 */
export const difyClient = new DifyClient();

/**
 * 错误处理辅助函数
 */
export function isDifyError(response: DifyApiResponse<any>): response is { success: false; error: string; details?: string } {
  return !response.success;
}

/**
 * 成功响应类型守卫
 */
export function isDifySuccess<T>(
  response: DifyApiResponse<T>
): response is { success: true; data: T } {
  return response.success;
}
