# UI - @kit/ui

This package is responsible for managing the UI components and styles across the app.

This package define two sets of components:

- `Shadcn UI`: A set of UI components that can be used across the app using shadcn UI
- `Makerkit-specific`: Components specific to MakerKit

## Installing a Shadcn UI component

 Please refer to the [documentation](https://makerkit.dev/docs/next-supabase-turbo/components/shadcn).