import { NextRequest, NextResponse } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';
import { generateUserId } from '~/lib/dify/types';

/**
 * 调试 API：检查 Dify 的原始响应格式
 * POST /api/debug/dify-response
 */
export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();
    
    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    const difyServices = getDifyServices();
    const userId = generateUserId();

    // 调用 Dify API
    const response = await difyServices.chatflow.createChatMessage(
      query.trim(),
      userId,
      undefined,
      {}
    );

    // 返回完整的原始响应用于调试
    return NextResponse.json({
      success: true,
      debug: {
        fullResponse: response,
        responseKeys: Object.keys(response),
        hasStructuredOutput: 'structured_output' in response,
        hasConversationId: 'conversationId' in response,
        hasConversation_id: 'conversation_id' in response,
        responseType: typeof response,
        responseConstructor: response.constructor.name
      }
    });

  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
