/**
 * 测试数据库连接和表结构
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少 Supabase 配置');
  console.error('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseKey ? '✅' : '❌');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔗 测试 Supabase 连接...');
  console.log('URL:', supabaseUrl);
  
  try {
    // 测试基本连接
    const { data, error } = await supabase.from('accounts').select('count').limit(1);
    
    if (error) {
      console.error('❌ 连接失败:', error.message);
      return false;
    }
    
    console.log('✅ Supabase 连接成功');
    return true;
  } catch (error) {
    console.error('❌ 连接异常:', error.message);
    return false;
  }
}

async function checkTableExists() {
  console.log('\n📋 检查 conversations_index 表是否存在...');
  
  try {
    const { data, error } = await supabase
      .from('conversations_index')
      .select('count')
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') {
        console.log('❌ conversations_index 表不存在');
        return false;
      } else {
        console.error('❌ 查询错误:', error.message);
        return false;
      }
    }
    
    console.log('✅ conversations_index 表存在');
    return true;
  } catch (error) {
    console.error('❌ 检查表异常:', error.message);
    return false;
  }
}

async function createTable() {
  console.log('\n🔨 尝试创建 conversations_index 表...');
  
  const createTableSQL = `
    -- 创建对话索引表
    CREATE TABLE IF NOT EXISTS public.conversations_index (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      
      -- Dify 对话 ID（来自 Dify API）
      conversation_id TEXT NOT NULL UNIQUE,
      
      -- 短链接 ID（Base58 编码的 UUID）
      short_id TEXT NOT NULL UNIQUE,
      
      -- URL 友好的标识符（AI 生成）
      handle TEXT NOT NULL,
      
      -- 问题标题（AI 生成）
      title TEXT NOT NULL,
      
      -- 问题描述（AI 生成）
      description TEXT NOT NULL,
      
      -- 语言标识
      language VARCHAR(10) NOT NULL DEFAULT 'zh',
      
      -- 学科分类 ID（AI 生成）
      category_id INTEGER NOT NULL DEFAULT 99,
      
      -- 用户 ID（如果是注册用户）
      user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
      
      -- 可见性设置
      is_public BOOLEAN NOT NULL DEFAULT false,
      
      -- 用户类型：0=游客, 1=注册用户
      user_type SMALLINT NOT NULL DEFAULT 0 CHECK (user_type IN (0, 1)),
      
      -- 统计数据
      view_count INTEGER NOT NULL DEFAULT 0,
      like_count INTEGER NOT NULL DEFAULT 0,
      
      -- 时间戳
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error('❌ 创建表失败:', error.message);
      return false;
    }
    
    console.log('✅ 表创建成功');
    return true;
  } catch (error) {
    console.error('❌ 创建表异常:', error.message);
    return false;
  }
}

async function createIndexes() {
  console.log('\n📊 创建索引...');
  
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_conversation_id ON public.conversations_index(conversation_id);',
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_short_id ON public.conversations_index(short_id);',
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_handle ON public.conversations_index(handle);',
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_user_id ON public.conversations_index(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_public_category ON public.conversations_index(is_public, category_id);',
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_language ON public.conversations_index(language);',
    'CREATE INDEX IF NOT EXISTS idx_conversations_index_created_at ON public.conversations_index(created_at DESC);',
  ];
  
  for (const indexSQL of indexes) {
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: indexSQL });
      if (error) {
        console.error('❌ 创建索引失败:', error.message);
      } else {
        console.log('✅ 索引创建成功');
      }
    } catch (error) {
      console.error('❌ 创建索引异常:', error.message);
    }
  }
}

async function testInsert() {
  console.log('\n🧪 测试插入数据...');
  
  const testData = {
    conversation_id: 'test-' + Date.now(),
    short_id: 'test-short-' + Date.now(),
    handle: 'test-handle',
    title: '测试标题',
    description: '测试描述',
    language: 'zh',
    category_id: 99,
    user_id: null,
    is_public: true,
    user_type: 0,
    view_count: 0,
    like_count: 0,
  };
  
  try {
    const { data, error } = await supabase
      .from('conversations_index')
      .insert(testData)
      .select()
      .single();
    
    if (error) {
      console.error('❌ 插入数据失败:', error.message);
      return false;
    }
    
    console.log('✅ 插入数据成功:', data.id);
    
    // 清理测试数据
    await supabase
      .from('conversations_index')
      .delete()
      .eq('id', data.id);
    
    console.log('✅ 清理测试数据成功');
    return true;
  } catch (error) {
    console.error('❌ 插入数据异常:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 开始数据库测试...\n');
  
  // 1. 测试连接
  const connected = await testConnection();
  if (!connected) {
    console.log('❌ 数据库连接失败，停止测试');
    return;
  }
  
  // 2. 检查表是否存在
  const tableExists = await checkTableExists();
  
  // 3. 如果表不存在，尝试创建
  if (!tableExists) {
    const created = await createTable();
    if (!created) {
      console.log('❌ 无法创建表，停止测试');
      return;
    }
    
    // 创建索引
    await createIndexes();
  }
  
  // 4. 测试插入数据
  const insertSuccess = await testInsert();
  
  if (insertSuccess) {
    console.log('\n🎉 所有测试通过！数据库已准备就绪。');
  } else {
    console.log('\n❌ 数据库测试失败。');
  }
}

main().catch(console.error);
