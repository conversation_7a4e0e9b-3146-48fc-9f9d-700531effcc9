'use client';

import { useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { 
  Heart, 
  Share2, 
  CheckCircle, 
  Calendar,
  User,
  MessageSquare,
  Loader2,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

import {
  StaticAnswerProps
} from '~/lib/types/components';
import { ConversationIndexRow, AnswerStatus } from '~/lib/types/conversations';
import { useStaticAnswerState, createComponentError } from '~/lib/hooks/use-component-state';

/**
 * StaticAnswer 组件
 * 功能：显示已完成的完整答案
 * 输入：短ID或会话数据
 * 状态：适用于 answer_status = completed
 * 独立性：纯展示组件，支持Markdown渲染
 */
export function StaticAnswer({
  shortId,
  conversationData,
  content,
  enableMarkdown = true,
  showActions = true,
  showMetadata = true,
  onLike,
  onShare,
  onError,
  className = '',
  showLoading = true
}: StaticAnswerProps) {
  const { state, actions } = useStaticAnswerState();

  // 加载会话数据
  const loadConversationData = useCallback(async () => {
    if (conversationData) {
      actions.setConversationData(conversationData);
      actions.setContent(conversationData.description || '');
      return;
    }

    if (content) {
      actions.setContent(content);
      return;
    }

    if (!shortId) {
      const error = createComponentError(
        'MISSING_DATA',
        '缺少必要的数据：shortId、conversationData 或 content'
      );
      actions.setError(error);
      onError?.(error);
      return;
    }

    try {
      const response = await fetch(`/api/conversations/${shortId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || '获取会话数据失败');
      }

      const data: ConversationIndexRow = result.data;
      
      // 验证数据状态
      if (data.answer_status !== AnswerStatus.COMPLETED) {
        const error = createComponentError(
          'INVALID_STATUS',
          `会话状态不正确：${data.answer_status}，期望：${AnswerStatus.COMPLETED}`
        );
        actions.setError(error);
        onError?.(error);
        return;
      }

      if (!data.description) {
        const error = createComponentError(
          'NO_CONTENT',
          '会话没有答案内容'
        );
        actions.setError(error);
        onError?.(error);
        return;
      }

      actions.setConversationData(data);
      actions.setContent(data.description);
      actions.setLikeCount(data.like_count || 0);

    } catch (error) {
      const componentError = createComponentError(
        'LOAD_ERROR',
        '加载会话数据失败',
        String(error)
      );
      actions.setError(componentError);
      onError?.(componentError);
    }
  }, [shortId, conversationData, content, actions, onError]);

  // 初始化加载
  useEffect(() => {
    loadConversationData();
  }, [loadConversationData]);

  // 处理点赞
  const handleLike = useCallback(async () => {
    if (!shortId || !onLike) return;

    try {
      // 乐观更新
      actions.setLiked(!state.isLiked);
      actions.setLikeCount(state.isLiked ? state.likeCount - 1 : state.likeCount + 1);

      onLike(shortId);

    } catch (error) {
      // 回滚乐观更新
      actions.setLiked(state.isLiked);
      actions.setLikeCount(state.likeCount);
      
      const componentError = createComponentError(
        'LIKE_ERROR',
        '点赞操作失败',
        String(error)
      );
      actions.setError(componentError);
      onError?.(componentError);
    }
  }, [shortId, onLike, state.isLiked, state.likeCount, actions, onError]);

  // 处理分享
  const handleShare = useCallback(async () => {
    if (!shortId || !onShare) return;

    const url = `${window.location.origin}/questions/${shortId}`;
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: '查看这个AI回答',
          text: state.content.substring(0, 100) + '...',
          url: url
        });
      } else {
        await navigator.clipboard.writeText(url);
        // 这里可以添加一个toast提示
      }

      onShare(shortId, url);

    } catch (error) {
      const componentError = createComponentError(
        'SHARE_ERROR',
        '分享操作失败',
        String(error)
      );
      actions.setError(componentError);
      onError?.(componentError);
    }
  }, [shortId, onShare, state.content, actions, onError]);

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 加载状态
  if (showLoading && state.loadingState === 'loading') {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <div className="text-center">
              <h3 className="font-medium">加载中...</h3>
              <p className="text-sm text-muted-foreground mt-1">
                正在获取答案内容
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 错误状态
  if (state.error) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <div className="text-center">
              <h3 className="font-medium text-destructive">
                {state.error.message}
              </h3>
              {state.error.details && (
                <p className="text-sm text-destructive/80 mt-1">
                  {state.error.details}
                </p>
              )}
              <p className="text-xs text-muted-foreground mt-2">
                错误代码: {state.error.code}
              </p>
            </div>
            <Button
              onClick={loadConversationData}
              variant="outline"
              size="sm"
            >
              重新加载
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 没有内容
  if (!state.content) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            <MessageSquare className="h-8 w-8 mx-auto mb-2" />
            <p>暂无答案内容</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg">AI 回答</CardTitle>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              已完成
            </Badge>
          </div>

          {showActions && (
            <div className="flex items-center gap-2">
              {onLike && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLike}
                  className={`flex items-center gap-1 ${
                    state.isLiked ? 'text-red-600' : 'text-muted-foreground'
                  }`}
                >
                  <Heart className={`h-4 w-4 ${state.isLiked ? 'fill-current' : ''}`} />
                  {state.likeCount > 0 && <span>{state.likeCount}</span>}
                </Button>
              )}

              {onShare && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="flex items-center gap-1 text-muted-foreground"
                >
                  <Share2 className="h-4 w-4" />
                  分享
                </Button>
              )}

              {shortId && (
                <Button
                  variant="ghost"
                  size="sm"
                  asChild
                  className="flex items-center gap-1 text-muted-foreground"
                >
                  <a href={`/questions/${shortId}`} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4" />
                    查看详情
                  </a>
                </Button>
              )}
            </div>
          )}
        </div>

        {showMetadata && state.conversationData && (
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {formatDate(state.conversationData.created_at)}
            </div>
            
            {state.conversationData.ai_model && (
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {state.conversationData.ai_model}
              </div>
            )}

            <div className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              {state.content.length} 字符
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div className="prose prose-sm max-w-none">
          {enableMarkdown ? (
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // 自定义组件样式
                h1: ({ children }) => <h1 className="text-xl font-bold mb-4">{children}</h1>,
                h2: ({ children }) => <h2 className="text-lg font-semibold mb-3">{children}</h2>,
                h3: ({ children }) => <h3 className="text-base font-medium mb-2">{children}</h3>,
                p: ({ children }) => <p className="mb-3 leading-relaxed">{children}</p>,
                ul: ({ children }) => <ul className="list-disc list-inside mb-3 space-y-1">{children}</ul>,
                ol: ({ children }) => <ol className="list-decimal list-inside mb-3 space-y-1">{children}</ol>,
                li: ({ children }) => <li className="leading-relaxed">{children}</li>,
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-primary/20 pl-4 italic text-muted-foreground mb-3">
                    {children}
                  </blockquote>
                ),
                code: ({ children, className }) => {
                  const isInline = !className;
                  return isInline ? (
                    <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
                      {children}
                    </code>
                  ) : (
                    <code className={`block bg-muted p-3 rounded text-sm font-mono overflow-x-auto ${className}`}>
                      {children}
                    </code>
                  );
                }
              }}
            >
              {state.content}
            </ReactMarkdown>
          ) : (
            <div className="whitespace-pre-wrap leading-relaxed">
              {state.content}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
