async function testDifyTraceId() {
  console.log('=== 直接测试 Dify API trace_id 功能 ===');
  
  // 生成测试短ID
  const testShortId = 'TRACE' + Math.random().toString(36).substring(2, 8).toUpperCase();
  console.log('测试 trace_id:', testShortId);
  
  // Dify API 配置
  const DIFY_API_KEY = 'app-3vhk2SEBvLEXrgDL8hf6CwVR';
  const DIFY_BASE_URL = 'https://api2.buyvs.com/v1';
  
  try {
    console.log('\n1. 测试通过 Header 传递 trace_id...');
    
    const response = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DIFY_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Trace-Id': testShortId  // 通过 Header 传递
      },
      body: JSON.stringify({
        inputs: {},
        query: `What is 3+3? Please include trace_id ${testShortId} in your response.`,
        response_mode: 'streaming',
        conversation_id: '',
        user: 'test-user-' + Date.now()
      })
    });
    
    console.log('Dify API 响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Dify API 错误:', errorText);
      return;
    }
    
    if (response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let foundTraceId = false;
      let conversationId = '';
      let fullAnswer = '';
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                // 检查 trace_id
                if (data.trace_id) {
                  console.log('🎯 找到 trace_id:', data.trace_id);
                  if (data.trace_id === testShortId) {
                    console.log('✅ trace_id 匹配!');
                    foundTraceId = true;
                  }
                }
                
                // 收集会话ID
                if (data.conversation_id) {
                  conversationId = data.conversation_id;
                }
                
                // 收集答案
                if (data.answer) {
                  fullAnswer += data.answer;
                  if (data.answer.includes(testShortId)) {
                    console.log('📝 在答案中找到 trace_id');
                  }
                }
                
                // 显示关键事件
                if (data.event) {
                  console.log(`📨 事件: ${data.event}`);
                  if (data.event === 'message_end') {
                    console.log('📄 完整数据:', JSON.stringify(data, null, 2));
                  }
                }
                
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
      
      console.log('\n=== 直接 Dify API 测试结果 ===');
      console.log('发送的 trace_id:', testShortId);
      console.log('找到 trace_id:', foundTraceId ? '✅ 是' : '❌ 否');
      console.log('会话ID:', conversationId);
      console.log('完整答案:', fullAnswer.substring(0, 200) + (fullAnswer.length > 200 ? '...' : ''));
    }
    
    // 测试通过 body 传递 trace_id
    console.log('\n2. 测试通过 body 传递 trace_id...');
    
    const testShortId2 = 'BODY' + Math.random().toString(36).substring(2, 8).toUpperCase();
    console.log('测试 trace_id (body):', testShortId2);
    
    const response2 = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DIFY_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        inputs: {},
        query: `What is 5+5? Please include trace_id ${testShortId2} in your response.`,
        response_mode: 'streaming',
        conversation_id: '',
        user: 'test-user-' + Date.now(),
        trace_id: testShortId2  // 通过 body 传递
      })
    });
    
    console.log('Dify API 响应状态 (body):', response2.status);
    
    if (response2.ok && response2.body) {
      const reader2 = response2.body.getReader();
      const decoder2 = new TextDecoder();
      let foundTraceId2 = false;
      
      try {
        while (true) {
          const { done, value } = await reader2.read();
          if (done) break;
          
          const chunk = decoder2.decode(value, { stream: true });
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.trace_id === testShortId2) {
                  console.log('✅ body trace_id 匹配!');
                  foundTraceId2 = true;
                }
                
                if (data.event === 'message_end') {
                  console.log('📄 body 测试完整数据:', JSON.stringify(data, null, 2));
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
      } finally {
        reader2.releaseLock();
      }
      
      console.log('body 方式找到 trace_id:', foundTraceId2 ? '✅ 是' : '❌ 否');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testDifyTraceId();
