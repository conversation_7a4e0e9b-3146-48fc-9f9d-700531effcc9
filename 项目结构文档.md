# AI作业助手平台 - 项目结构文档

## 项目架构概述

### 🏗️ 基础架构
- **基础框架**：Makerkit SaaS Starter Kit v2.12.2
- **架构模式**：Turbo Monorepo
- **主要技术**：Next.js 15 + Supabase + TypeScript
- **包管理**：pnpm workspace

### 📁 根目录结构
```
ai-homework-helper/
├── apps/                          # 应用程序目录
│   ├── web/                       # 主 Web 应用
│   ├── dev-tool/                  # 开发工具
│   └── e2e/                       # 端到端测试
├── packages/                      # 共享包目录
│   ├── ui/                        # UI 组件库
│   ├── auth/                      # 认证功能
│   ├── billing/                   # 计费功能
│   ├── supabase/                  # Supabase 集成
│   └── ...                        # 其他功能包
├── tooling/                       # 工具配置
│   ├── eslint/                    # ESLint 配置
│   ├── prettier/                  # Prettier 配置
│   └── typescript/                # TypeScript 配置
├── turbo.json                     # Turbo 配置
├── pnpm-workspace.yaml            # pnpm 工作空间配置
└── package.json                   # 根包配置
```

## 主应用结构 (apps/web)

### 📂 核心目录
```
apps/web/
├── app/                           # Next.js 14 App Router
│   ├── (marketing)/               # 营销页面组
│   │   ├── page.tsx              # 主页 (我们的聊天界面)
│   │   ├── layout.tsx            # 营销布局
│   │   └── _components/          # 页面组件
│   ├── api/                      # API 路由
│   │   ├── chat/                 # 聊天 API (自定义)
│   │   └── upload/               # 上传 API (自定义)
│   ├── globals.css               # 全局样式
│   └── layout.tsx                # 根布局
├── lib/                          # 工具库
│   ├── dify/                     # Dify 服务 (自定义)
│   └── ...                       # 其他工具
├── components/                   # 共享组件
├── config/                       # 配置文件
├── public/                       # 静态资源
├── package.json                  # 应用依赖
└── next.config.js               # Next.js 配置
```

## 自定义代码清单

### ✅ 我们创建/修改的文件

#### 1. AI 聊天功能 (核心自定义代码)
```
apps/web/app/(marketing)/
├── page.tsx                      # ✨ 修改：主页聊天界面
├── _components/
│   ├── chat-section.tsx          # ✨ 新增：聊天主组件
│   ├── chat-input.tsx            # ✨ 新增：消息输入组件
│   ├── ai-response.tsx           # ✨ 新增：AI回答展示
│   ├── file-upload.tsx           # ✨ 新增：文件上传组件
│   ├── loading-states.tsx        # ✨ 新增：加载状态组件
│   └── error-handling.tsx        # ✨ 新增：错误处理组件
```

#### 2. API 接口 (完全自定义)
```
apps/web/app/api/
├── chat/
│   └── route.ts                  # ✨ 新增：Dify 聊天 API，集成结构化输出解析和自动保存
├── conversations/
│   └── save/
│       └── route.ts              # ✨ 新增：对话数据保存和查询 API
└── debug/
    └── dify-response/
        └── route.ts              # ✨ 新增：Dify 响应调试 API
```

#### 2. Dify 服务集成 (完全自定义)
```
apps/web/lib/dify/
├── dify-service.ts               # ✨ 新增：Dify 服务封装，支持聊天、流式、文件上传
└── types.ts                      # ✨ 新增：Dify API 类型定义，包含结构化输出类型
```

#### 3. 数据库工具 (完全自定义)
```
apps/web/lib/utils/
└── short-id.ts                   # ✨ 新增：Base58 短链接生成工具
```

#### 4. 数据库迁移 (完全自定义)
```
apps/web/supabase/migrations/
└── 20250128000001_conversations_index.sql  # ✨ 新增：对话索引表迁移
```

#### 5. 配置文件修改
```
apps/web/
├── .env.development              # ✨ 修改：添加 Dify API 配置
└── .env.local                    # ✨ 新增：本地环境变量配置
```

#### 6. 项目文档 (完全自定义)
```
根目录/
├── 项目结构文档.md               # ✨ 新增：本文档
├── 存储提问数据.md               # ✨ 新增：数据存储设计文档
├── 备注.md                       # ✨ 新增：开发备注
└── 参考网站.md                   # ✨ 新增：参考资料链接
```

### 🏗️ Makerkit 原生文件 (未修改)

#### 1. 核心框架文件
```
apps/web/
├── app/
│   ├── (app)/                    # 🔧 Makerkit：应用页面
│   ├── (auth)/                   # 🔧 Makerkit：认证页面
│   ├── admin/                    # 🔧 Makerkit：管理页面
│   └── layout.tsx                # 🔧 Makerkit：根布局 (可能有小修改)
├── components/                   # 🔧 Makerkit：基础组件
├── lib/                          # 🔧 Makerkit：工具库 (除了 dify/)
└── middleware.ts                 # 🔧 Makerkit：中间件
```

#### 2. 包系统 (完全未修改)
```
packages/
├── ui/                           # 🔧 Makerkit：UI 组件库
├── auth/                         # 🔧 Makerkit：认证系统
├── billing/                      # 🔧 Makerkit：计费系统
├── supabase/                     # 🔧 Makerkit：Supabase 集成
├── i18n/                         # 🔧 Makerkit：国际化
└── ...                           # 🔧 Makerkit：其他功能包
```

#### 3. 工具配置 (基本未修改)
```
tooling/
├── eslint/                       # 🔧 Makerkit：ESLint 配置
├── prettier/                     # 🔧 Makerkit：Prettier 配置
└── typescript/                   # 🔧 Makerkit：TypeScript 配置
```

## 依赖关系分析

### 📦 新增依赖 (我们添加的)
```json
{
  "dependencies": {
    "dify-client": "^2.3.2",           // Dify API 客户端
    "react-markdown": "^10.1.0",       // Markdown 渲染
    "rehype-highlight": "^7.0.2",      // 代码高亮
    "rehype-katex": "^7.0.1",          // 数学公式
    "remark-gfm": "^4.0.1",            // GitHub 风格 Markdown
    "remark-math": "^6.0.0"            // 数学公式解析
  }
}
```

### 🏗️ Makerkit 核心依赖 (框架自带)
```json
{
  "dependencies": {
    "@kit/*": "workspace:*",           // Makerkit 内部包
    "@supabase/supabase-js": "2.52.0", // Supabase 客户端
    "next": "15.4.3",                  // Next.js 框架
    "react": "19.1.0",                 // React
    "tailwindcss": "4.1.11",           // Tailwind CSS
    "@tanstack/react-query": "5.83.0", // 数据获取
    "lucide-react": "^0.525.0"         // 图标库
  }
}
```

## 代码组织规范

### 🎯 文件命名规范
- **组件文件**：kebab-case (如 `chat-section.tsx`)
- **API 路由**：RESTful 风格 (如 `api/chat/route.ts`)
- **工具文件**：camelCase (如 `difyService.ts`)
- **类型文件**：PascalCase 接口 (如 `ChatMessage`)

### 📁 目录组织原则
1. **功能优先**：按功能模块组织代码
2. **层次清晰**：API、组件、服务分层明确
3. **复用性**：共享组件放在 `_components/`
4. **可维护性**：相关文件就近放置

### 🔧 代码分离策略
1. **UI 组件**：纯展示组件，无业务逻辑
2. **业务逻辑**：集中在服务层 (`lib/dify/`)
3. **API 接口**：标准化的 RESTful API
4. **类型定义**：集中管理，便于复用

## 升级和维护策略

### 🔄 Makerkit 框架升级
1. **版本跟踪**：定期检查 Makerkit 更新
2. **影响评估**：评估升级对自定义代码的影响
3. **测试验证**：升级后全面测试自定义功能
4. **回滚准备**：保持版本控制，支持快速回滚

### 🛡️ 代码解耦策略
1. **接口抽象**：使用接口隔离 Makerkit 依赖
2. **配置外置**：环境变量和配置文件管理
3. **功能模块化**：自定义功能独立模块
4. **文档维护**：清晰记录修改和依赖关系

### 📋 升级检查清单
- [ ] 检查 Makerkit 版本更新日志
- [ ] 评估对自定义 API 的影响
- [ ] 测试 Dify 集成功能
- [ ] 验证 UI 组件兼容性
- [ ] 检查依赖包版本冲突
- [ ] 更新项目文档

---

*最后更新：2025年1月28日*
