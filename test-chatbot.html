<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Homework Helper - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .container {
            flex: 1;
            display: flex;
            padding: 20px;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .info-panel {
            flex: 0 0 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .info-panel h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .feature-list li {
            padding: 8px 0;
            color: #555;
            display: flex;
            align-items: center;
        }

        .feature-list li::before {
            content: "✨";
            margin-right: 10px;
        }

        .usage-tips {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .usage-tips h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .usage-tips p {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .chatbot-container {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .chatbot-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chatbot-header::before {
            content: "🤖";
            font-size: 1.2rem;
        }

        .chatbot-iframe {
            width: 100%;
            height: calc(700px - 60px);
            border: none;
            display: block;
        }

        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                padding: 10px;
            }

            .info-panel {
                flex: none;
                order: 2;
            }

            .chatbot-container {
                order: 1;
                min-height: 600px;
            }

            .chatbot-iframe {
                height: calc(600px - 60px);
            }

            .header h1 {
                font-size: 2rem;
            }
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎓 AI Homework Helper</h1>
        <p>智能作业助手测试平台 - 让学习更轻松</p>
    </div>

    <div class="container">
        <div class="info-panel">
            <h3>🚀 功能特色</h3>
            <ul class="feature-list">
                <li>多学科问题解答</li>
                <li>图片题目识别</li>
                <li>详细解题步骤</li>
                <li>知识点拓展</li>
                <li>学习方法指导</li>
                <li>24/7 在线服务</li>
            </ul>

            <div class="usage-tips">
                <h4>💡 使用提示</h4>
                <p>📷 可以直接上传题目图片</p>
                <p>📝 支持文字描述问题</p>
                <p>🔄 图片+文字组合提问</p>
                <p>🎯 支持追问和深入讨论</p>
                <p>📚 涵盖数学、物理、化学等学科</p>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                <div style="display: flex; align-items: center; color: #155724;">
                    <span>🟢 服务状态：在线</span>
                    <div class="status-indicator"></div>
                </div>
                <div style="font-size: 0.8rem; color: #155724; margin-top: 5px;">
                    基于 Dify AI 平台构建
                </div>
            </div>
        </div>

        <div class="chatbot-container">
            <div class="chatbot-header">
                AI 作业助手聊天窗口
            </div>
            <iframe
                src="https://web-production-d38fb.up.railway.app/chatbot/7mxipIGlDBd0WIaN"
                class="chatbot-iframe"
                allow="microphone">
            </iframe>
        </div>
    </div>

    <div class="footer">
        <p>© 2024 AI Homework Helper | 基于 Dify 平台 | 让每个学生都能享受智能学习体验</p>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI Homework Helper 测试页面已加载');
            
            // 检查 iframe 加载状态
            const iframe = document.querySelector('.chatbot-iframe');
            iframe.addEventListener('load', function() {
                console.log('Chatbot iframe 加载完成');
            });

            iframe.addEventListener('error', function() {
                console.error('Chatbot iframe 加载失败');
                // 可以在这里添加错误处理逻辑
            });
        });

        // 简单的页面统计
        let startTime = Date.now();
        window.addEventListener('beforeunload', function() {
            const sessionTime = Math.round((Date.now() - startTime) / 1000);
            console.log(`用户会话时长: ${sessionTime} 秒`);
        });
    </script>
</body>
</html>
