{"name": "web", "version": "0.1.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"analyze": "ANALYZE=true pnpm run build", "build": "next build", "build:test": "NODE_ENV=test next build --turbopack", "clean": "git clean -xdf .next .turbo node_modules", "dev": "next dev --turbo | pino-pretty -c", "lint": "eslint .", "lint:fix": "next lint --fix", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "start": "next start", "start:test": "NODE_ENV=test next start", "typecheck": "tsc --noEmit", "supabase": "supabase", "supabase:start": "supabase status || supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:db:diff": "supabase db diff", "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts", "supabase:db:dump:local": "supabase db dump --local --data-only"}, "dependencies": {"@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@hookform/resolvers": "^5.1.1", "@kit/accounts": "workspace:*", "@kit/admin": "workspace:*", "@kit/analytics": "workspace:*", "@kit/auth": "workspace:*", "@kit/billing": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/cms": "workspace:*", "@kit/database-webhooks": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/i18n": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/next": "workspace:*", "@kit/notifications": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/ui": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.10", "@makerkit/data-loader-supabase-nextjs": "^1.2.5", "@marsidev/react-turnstile": "^1.1.0", "@nosecone/next": "1.0.0-beta.9", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "2.52.0", "@tanstack/react-query": "5.83.0", "@tanstack/react-table": "^8.21.3", "date-fns": "^4.1.0", "dify-client": "^2.3.2", "lucide-react": "^0.525.0", "next": "15.4.3", "next-sitemap": "^4.2.3", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.1", "recharts": "2.15.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.74"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@next/bundle-analyzer": "15.4.3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.15", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "babel-plugin-react-compiler": "19.1.0-rc.2", "cssnano": "^7.1.0", "pino-pretty": "^13.0.0", "prettier": "^3.6.2", "supabase": "^2.31.8", "tailwindcss": "4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"]}