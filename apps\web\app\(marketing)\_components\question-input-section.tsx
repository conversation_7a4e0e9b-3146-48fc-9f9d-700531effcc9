'use client';

import { useRouter } from 'next/navigation';
import { QuestionInput } from '~/components/question-input';

/**
 * 问题输入区域组件
 * 客户端组件，处理路由跳转逻辑
 */
export function QuestionInputSection() {
  const router = useRouter();

  // 处理问题提交成功
  const handleQuestionSubmit = (shortId: string) => {
    // 立即跳转到问题详情页面
    router.push(`/questions/${shortId}`);
  };

  // 处理提交错误
  const handleSubmitError = (error: any) => {
    console.error('Question submit error:', error);
    // 这里可以添加错误提示，比如toast
  };

  return (
    <QuestionInput
      onSubmit={handleQuestionSubmit}
      onError={handleSubmitError}
      enableFileUpload={true}
      className="max-w-4xl mx-auto"
    />
  );
}
