{"name": "@kit/{{ name }}", "private": true, "version": "0.1.0", "exports": {".": "./index.ts"}, "typesVersions": {"*": {"*": ["src/*"]}}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "eslint .", "format": "prettier --check \"**/*.{mjs,ts,md,json}\"", "typecheck": "tsc --noEmit"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*"}, "prettier": "@kit/prettier-config"}