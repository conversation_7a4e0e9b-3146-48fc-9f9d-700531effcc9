import { NextRequest, NextResponse } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';
import {
  ChatRequest,
  DifyApiError,
  DifyMessage,
  generateUserId
} from '~/lib/dify/types';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { ConversationService } from '~/lib/services/conversation-service';
import { generateUniqueShortId } from '~/lib/utils/short-id';
import { MetadataStatus, ClientType } from '~/lib/types/conversations';

/**
 * 新架构聊天 API Route
 * POST /api/chat
 *
 * 流程：
 * 1. 生成短ID并预写入数据库
 * 2. 启动主工作流（流式响应）
 * 3. 获得 conversation_id 后更新数据库
 * 4. 异步启动元数据处理
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body: ChatRequest & { shortId?: string } = await request.json();
    const { query, conversationId, files, inputs, shortId } = body;

    // 验证必需参数
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Query is required and cannot be empty' },
        { status: 400 }
      );
    }

    // 获取或生成用户 ID
    const userId = generateUserId();
    console.log('=== New Chat API Debug ===');
    console.log('Generated userId:', userId);
    console.log('Existing conversationId:', conversationId);
    console.log('Provided shortId:', shortId);

    // 获取服务实例
    const difyServices = getDifyServices();
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    let currentShortId = shortId;

    // 如果没有提供 shortId，创建新的会话记录
    if (!currentShortId) {
      currentShortId = await generateUniqueShortId(async (id: string) => {
        const { data } = await supabase
          .from('conversations_index')
          .select('id')
          .eq('short_id', id)
          .maybeSingle();
        return !data;
      });

      // 预写入数据库
      const createResult = await conversationService.createConversation({
        short_id: currentShortId,
        user_id: null, // 游客模式
        user_type: 0,
        is_public: true,
        metadata_status: MetadataStatus.PENDING,
        client_type: ClientType.WEB,
        ai_model: 'gpt-4.1-mini',
      });

      if (!createResult.success) {
        throw new Error(`Failed to create conversation: ${createResult.error}`);
      }

      console.log('Created new conversation with shortId:', currentShortId);
    }

    // 准备文件参数（暂时保留兼容性）
    const difyFiles = files?.map(file => ({
      type: file.type,
      transfer_method: file.uploadFileId ? 'local_file' as const : 'remote_url' as const,
      url: file.url,
      upload_file_id: file.uploadFileId,
    })) || [];

    console.log('Starting streaming chat with params:');
    console.log('- query:', query.trim());
    console.log('- conversationId:', conversationId);
    console.log('- userId:', userId);
    console.log('- shortId:', currentShortId);

    // 创建流式响应
    const encoder = new TextEncoder();
    let finalConversationId: string | null = null;
    let finalAnswer = '';

    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 启动主工作流（流式聊天）
          const chatStream = difyServices.chatflow.chatStream(
            query.trim(),
            userId,
            conversationId,
            inputs || {}
          );

          for await (const chunk of chatStream) {
            // 处理不同类型的流式数据
            if (chunk.event === 'message') {
              if (chunk.answer) {
                finalAnswer += chunk.answer;
                // 发送答案片段给前端
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                  type: 'answer',
                  content: chunk.answer
                })}\n\n`));
              }
            } else if (chunk.event === 'message_end') {
              if (chunk.conversation_id) {
                finalConversationId = chunk.conversation_id;
              }

              // 发送完成信号
              controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                type: 'done',
                conversation_id: finalConversationId,
                short_id: currentShortId
              })}\n\n`));
            }
          }

          // 更新数据库中的 conversation_id
          if (finalConversationId && currentShortId) {
            await conversationService.updateConversationId(
              currentShortId,
              finalConversationId,
              userId
            );
            console.log('Updated conversation_id in database:', finalConversationId);

            // 异步启动元数据处理
            fetch(`${request.nextUrl.origin}/api/chat/metadata`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                conversation_id: finalConversationId,  // Dify 对话ID
                short_id: currentShortId,              // 系统短ID
                query: query.trim(),
                answer: finalAnswer
              })
            }).catch(error => {
              console.error('Failed to start metadata processing:', error);
            });
          }

          controller.close();
        } catch (error) {
          console.error('Stream error:', error);
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })}\n\n`));
          controller.close();
        }
      }
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Chat API error:', error);

    // 处理 Dify API 错误
    if (error instanceof DifyApiError) {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code || 'DIFY_API_ERROR'
        },
        { status: 500 }
      );
    }

    // 处理其他错误
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
        type: 'internal_error'
      },
      { status: 500 }
    );
  }
}
