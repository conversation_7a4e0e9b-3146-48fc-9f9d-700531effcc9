'use client';

import { <PERSON>ader2, <PERSON>, <PERSON><PERSON><PERSON>, MessageSquare } from 'lucide-react';
import { Card, CardContent } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <Loader2 className={cn('animate-spin', sizeClasses[size], className)} />
  );
}

interface TypingIndicatorProps {
  className?: string;
}

export function TypingIndicator({ className }: TypingIndicatorProps) {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
      </div>
      <span className="text-sm text-muted-foreground">
        <Trans i18nKey="loading:aiThinking" defaults="AI is thinking..." />
      </span>
    </div>
  );
}

interface AIThinkingCardProps {
  stage?: 'analyzing' | 'processing' | 'generating';
  className?: string;
}

export function AIThinkingCard({ stage = 'processing', className }: AIThinkingCardProps) {
  const stageConfig = {
    analyzing: {
      icon: Brain,
      text: 'Analyzing your question...',
      color: 'text-blue-500',
    },
    processing: {
      icon: Sparkles,
      text: 'Processing with AI...',
      color: 'text-purple-500',
    },
    generating: {
      icon: MessageSquare,
      text: 'Generating response...',
      color: 'text-green-500',
    },
  };

  const config = stageConfig[stage];
  const Icon = config.icon;

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
              <Icon className={cn('w-6 h-6 text-white')} />
            </div>
            <div className="absolute -top-1 -right-1">
              <LoadingSpinner size="sm" className="text-primary" />
            </div>
          </div>
          
          <div className="flex-1">
            <h3 className="font-semibold text-foreground">StudyX AI</h3>
            <div className="flex items-center space-x-2 mt-1">
              <TypingIndicator />
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              <Trans i18nKey={`loading:${stage}`} defaults={config.text} />
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface ProgressBarProps {
  progress: number;
  className?: string;
  showPercentage?: boolean;
}

export function ProgressBar({ progress, className, showPercentage = false }: ProgressBarProps) {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-muted-foreground">
          <Trans i18nKey="loading:progress" defaults="Processing..." />
        </span>
        {showPercentage && (
          <span className="text-sm text-muted-foreground">{Math.round(progress)}%</span>
        )}
      </div>
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
}

interface SkeletonProps {
  className?: string;
  lines?: number;
}

export function Skeleton({ className, lines = 3 }: SkeletonProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'h-4 bg-muted rounded animate-pulse',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

export function LoadingOverlay({ isVisible, message, className }: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50',
      className
    )}>
      <div className="flex flex-col items-center space-y-4">
        <LoadingSpinner size="lg" />
        {message && (
          <p className="text-sm text-muted-foreground text-center max-w-xs">
            {message}
          </p>
        )}
      </div>
    </div>
  );
}
