# AI作业助手平台 - 升级策略文档

## 概述

本文档制定了基于 Makerkit SaaS 框架的 AI 作业助手平台的升级策略，确保我们的自定义代码与底层框架充分解耦，并建立了版本管理和升级检查清单。

## 当前架构分析

### 🏗️ 框架依赖关系
```
AI作业助手平台
├── Makerkit SaaS Kit v2.12.2 (基础框架)
│   ├── Next.js 15.4.3
│   ├── React 19.1.0
│   ├── Supabase 集成
│   ├── 认证系统 (@kit/auth)
│   ├── 计费系统 (@kit/billing)
│   ├── UI 组件库 (@kit/ui)
│   └── 其他 SaaS 功能
└── 自定义 AI 功能
    ├── Dify API 集成
    ├── 聊天界面组件
    ├── 文件上传功能
    └── AI 回答展示
```

### 🎯 解耦程度评估

#### ✅ 高度解耦 (安全升级)
- **Dify 服务集成**：完全独立的服务层
- **AI 聊天组件**：使用标准 React 模式
- **API 路由**：标准 Next.js API Routes
- **自定义样式**：基于 Tailwind CSS

#### ⚠️ 中度耦合 (需要注意)
- **UI 组件**：使用 @kit/ui 组件库
- **布局系统**：基于 Makerkit 布局结构
- **国际化**：使用 @kit/i18n 系统
- **路由结构**：遵循 Makerkit 约定

#### 🚨 高度耦合 (升级风险)
- **项目配置**：turbo.json, package.json 结构
- **构建系统**：Turbo monorepo 配置
- **依赖管理**：pnpm workspace 配置

## 升级策略

### 🔄 Makerkit 框架升级策略

#### 1. 版本跟踪机制
```bash
# 定期检查 Makerkit 更新
npm info @makerkit/next-supabase-saas-kit versions --json

# 查看更新日志
git log --oneline --since="1 month ago" origin/main

# 检查依赖更新
pnpm outdated
```

#### 2. 升级前评估清单
- [ ] **影响范围分析**
  - [ ] 检查 @kit/* 包的 API 变更
  - [ ] 评估 Next.js 版本兼容性
  - [ ] 检查 React 版本兼容性
  - [ ] 评估 Tailwind CSS 变更

- [ ] **自定义代码兼容性**
  - [ ] 测试 Dify 集成功能
  - [ ] 验证自定义 API 路由
  - [ ] 检查自定义组件
  - [ ] 测试文件上传功能

- [ ] **依赖冲突检查**
  - [ ] 检查 dify-client 兼容性
  - [ ] 验证 react-markdown 版本
  - [ ] 检查其他自定义依赖

#### 3. 升级执行步骤
```bash
# 1. 创建升级分支
git checkout -b upgrade/makerkit-v2.13.0

# 2. 备份当前配置
cp package.json package.json.backup
cp pnpm-lock.yaml pnpm-lock.yaml.backup

# 3. 更新 Makerkit 版本
pnpm update @kit/*

# 4. 解决依赖冲突
pnpm install

# 5. 运行测试
pnpm run typecheck
pnpm run lint
pnpm run build

# 6. 功能测试
pnpm run dev
# 手动测试 AI 聊天功能

# 7. 提交变更
git add .
git commit -m "upgrade: Makerkit to v2.13.0"
```

### 🛡️ 代码解耦改进计划

#### 1. 接口抽象层
```typescript
// lib/interfaces/ui.ts - UI 组件接口抽象
export interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

// components/ui/button.tsx - 适配器模式
import { Button as KitButton } from '@kit/ui/button';
export const Button: React.FC<ButtonProps> = (props) => {
  return <KitButton {...props} />;
};
```

#### 2. 配置外置
```typescript
// config/app.ts - 应用配置集中管理
export const appConfig = {
  ai: {
    provider: 'dify',
    apiKey: process.env.DIFY_API_KEY,
    baseUrl: process.env.DIFY_BASE_URL,
  },
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/*', 'application/pdf'],
  },
  ui: {
    theme: 'default',
    locale: 'zh-CN',
  },
};
```

#### 3. 服务层抽象
```typescript
// lib/services/ai-service.interface.ts
export interface AIService {
  sendMessage(params: ChatParams): Promise<ChatResponse>;
  uploadFile(file: File): Promise<UploadResponse>;
}

// lib/services/dify-ai-service.ts
export class DifyAIService implements AIService {
  // Dify 具体实现
}
```

### 📋 升级检查清单

#### 🔍 升级前检查
- [ ] **环境准备**
  - [ ] 备份当前代码
  - [ ] 记录当前版本号
  - [ ] 准备回滚计划
  - [ ] 通知团队成员

- [ ] **依赖分析**
  - [ ] 检查 Makerkit 更新日志
  - [ ] 分析破坏性变更
  - [ ] 评估升级工作量
  - [ ] 制定升级计划

#### 🚀 升级执行
- [ ] **代码更新**
  - [ ] 更新 Makerkit 版本
  - [ ] 解决依赖冲突
  - [ ] 修复类型错误
  - [ ] 更新配置文件

- [ ] **功能测试**
  - [ ] 编译测试
  - [ ] 单元测试
  - [ ] 集成测试
  - [ ] 手动功能测试

#### ✅ 升级后验证
- [ ] **核心功能验证**
  - [ ] AI 聊天功能正常
  - [ ] 文件上传功能正常
  - [ ] UI 界面显示正常
  - [ ] 响应式布局正常

- [ ] **性能验证**
  - [ ] 页面加载速度
  - [ ] API 响应时间
  - [ ] 内存使用情况
  - [ ] 构建时间

- [ ] **兼容性验证**
  - [ ] 浏览器兼容性
  - [ ] 移动端兼容性
  - [ ] 不同设备测试

### 🚨 回滚策略

#### 1. 快速回滚
```bash
# 回滚到升级前版本
git checkout main
git reset --hard HEAD~1

# 恢复依赖
cp package.json.backup package.json
cp pnpm-lock.yaml.backup pnpm-lock.yaml
pnpm install
```

#### 2. 部分回滚
```bash
# 只回滚特定包
pnpm add @kit/ui@2.12.2
pnpm add @kit/auth@2.12.2
```

#### 3. 紧急修复
- 识别具体问题组件
- 临时禁用有问题的功能
- 使用 feature flag 控制
- 准备热修复补丁

## 版本管理策略

### 📊 版本号规范
```
主版本.次版本.修订版本-框架版本
例如：1.2.3-mk2.12.2

主版本：重大功能变更
次版本：新功能添加
修订版本：Bug 修复
框架版本：Makerkit 版本
```

### 🏷️ 标签管理
```bash
# 发布标签
git tag -a v1.0.0-mk2.12.2 -m "Release v1.0.0 based on Makerkit 2.12.2"

# 升级标签
git tag -a v1.0.1-mk2.13.0 -m "Upgrade to Makerkit 2.13.0"
```

### 📝 变更日志
每次升级都要更新 `开发更新日志.md`：
- 记录 Makerkit 版本变更
- 记录自定义代码调整
- 记录已知问题和解决方案
- 记录性能影响

## 监控和告警

### 📈 升级后监控指标
- **功能可用性**：AI 聊天成功率
- **性能指标**：页面加载时间、API 响应时间
- **错误率**：JavaScript 错误、API 错误
- **用户体验**：页面跳出率、功能使用率

### 🚨 告警机制
- 功能异常自动告警
- 性能下降阈值告警
- 错误率超标告警
- 用户反馈收集

## 最佳实践

### ✅ 推荐做法
1. **渐进式升级**：先升级开发环境，再升级生产环境
2. **功能开关**：使用 feature flag 控制新功能
3. **自动化测试**：建立完整的测试套件
4. **文档同步**：升级后及时更新文档

### ❌ 避免做法
1. **直接生产升级**：跳过测试环境直接升级生产
2. **批量升级**：同时升级多个主要版本
3. **忽略测试**：升级后不进行充分测试
4. **缺乏备份**：升级前不备份代码和数据

---

*最后更新：2025年1月28日*
