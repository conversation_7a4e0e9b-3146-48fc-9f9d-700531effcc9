import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { MetadataMonitorService } from '~/lib/services/metadata-monitor-service';

/**
 * 获取元数据监控信息
 * GET /api/admin/metadata-monitor
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    const supabase = getSupabaseServerAdminClient();
    const monitorService = new MetadataMonitorService(supabase, request.nextUrl.origin);

    switch (action) {
      case 'failed':
        const failedTasks = await monitorService.getFailedTasks();
        return NextResponse.json({
          success: true,
          data: {
            type: 'failed_tasks',
            tasks: failedTasks,
            count: failedTasks.length
          }
        });

      case 'processing':
        const processingTasks = await monitorService.getProcessingTasks();
        return NextResponse.json({
          success: true,
          data: {
            type: 'processing_tasks',
            tasks: processingTasks,
            count: processingTasks.length
          }
        });

      case 'stuck':
        const stuckTasks = await monitorService.checkStuckTasks();
        return NextResponse.json({
          success: true,
          data: {
            type: 'stuck_tasks',
            tasks: stuckTasks,
            count: stuckTasks.length
          }
        });

      case 'overview':
      default:
        const [failed, processing, stuck] = await Promise.all([
          monitorService.getFailedTasks(),
          monitorService.getProcessingTasks(),
          monitorService.checkStuckTasks()
        ]);

        return NextResponse.json({
          success: true,
          data: {
            type: 'overview',
            summary: {
              failed_count: failed.length,
              processing_count: processing.length,
              stuck_count: stuck.length,
              total_issues: failed.length + stuck.length
            },
            failed_tasks: failed.slice(0, 10), // 只返回前10个
            processing_tasks: processing.slice(0, 10),
            stuck_tasks: stuck
          }
        });
    }
  } catch (error) {
    console.error('Metadata monitor GET error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get monitor data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * 执行元数据监控操作
 * POST /api/admin/metadata-monitor
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, shortId } = body;

    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'Action is required'
      }, { status: 400 });
    }

    const supabase = getSupabaseServerAdminClient();
    const monitorService = new MetadataMonitorService(supabase, request.nextUrl.origin);

    switch (action) {
      case 'retry_single':
        if (!shortId) {
          return NextResponse.json({
            success: false,
            error: 'shortId is required for retry_single action'
          }, { status: 400 });
        }

        const retryResult = await monitorService.retryFailedTask(shortId);
        return NextResponse.json({
          success: retryResult.success,
          data: {
            action: 'retry_single',
            shortId,
            result: retryResult
          },
          error: retryResult.error
        });

      case 'retry_all':
        const batchResult = await monitorService.retryAllFailedTasks();
        return NextResponse.json({
          success: true,
          data: {
            action: 'retry_all',
            result: batchResult
          }
        });

      case 'reset_stuck':
        const resetResult = await monitorService.resetStuckTasks();
        return NextResponse.json({
          success: true,
          data: {
            action: 'reset_stuck',
            result: resetResult
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Metadata monitor POST error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to execute monitor action',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * 删除失败的任务（标记为已处理）
 * DELETE /api/admin/metadata-monitor
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shortId = searchParams.get('shortId');

    if (!shortId) {
      return NextResponse.json({
        success: false,
        error: 'shortId is required'
      }, { status: 400 });
    }

    const supabase = getSupabaseServerAdminClient();
    
    // 将任务标记为已取消（不再重试）
    const { error } = await supabase
      .from('conversations_index')
      .update({
        metadata_status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('short_id', shortId);

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: {
        action: 'cancel_task',
        shortId,
        message: 'Task marked as cancelled'
      }
    });
  } catch (error) {
    console.error('Metadata monitor DELETE error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to cancel task',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
