'use client';

import { AlertCir<PERSON>, Bo<PERSON>, User, FileText, Image as ImageIcon } from 'lucide-react';
import { Card, CardContent } from '@kit/ui/card';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Copy } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

import { DifyConversationDetail } from '~/lib/services/dify-client';
import { DifyMessage } from '~/lib/dify/types';
import { 
  formatDifyMessage,
  isDifyUserMessage,
  isDifyAssistantMessage,
  getDifyMessageContent,
} from '~/lib/hooks/use-dify';

interface ConversationContentProps {
  conversationId: string;
  difyConversation?: DifyConversationDetail;
  isLoading: boolean;
  error: Error | null;
}

export function ConversationContent({
  conversationId,
  difyConversation,
  isLoading,
  error,
}: ConversationContentProps) {
  // 错误状态
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          无法加载对话内容：{error.message}
        </AlertDescription>
      </Alert>
    );
  }

  // 加载状态
  if (isLoading) {
    return <ConversationContentSkeleton />;
  }



  // 无对话内容
  if (!difyConversation || !difyConversation.messages?.length) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            暂无对话内容
          </h3>
          <p className="text-gray-600">
            这个问题还没有对话记录，或者对话内容无法访问。
          </p>
          <p className="text-xs text-gray-500 mt-2">
            调试信息：conversationId={conversationId},
            hasData={!!difyConversation},
            messageCount={difyConversation?.messages?.length || 0}
          </p>
        </CardContent>
      </Card>
    );
  }

  const messages = difyConversation.messages;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        对话内容
      </h2>
      
      <div className="space-y-4">
        {messages.map((message, index) => (
          <MessageCard key={message.id || index} message={message} />
        ))}
      </div>
    </div>
  );
}

/**
 * 单个消息卡片组件
 */
function MessageCard({ message }: { message: DifyMessage }) {
  const isUser = isDifyUserMessage(message);
  const isAssistant = isDifyAssistantMessage(message);
  const content = getDifyMessageContent(message);
  const formattedMessage = formatDifyMessage(message);

  if (!content) {
    return null;
  }

  return (
    <Card className={`${isUser ? 'ml-8' : 'mr-8'}`}>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          {/* 头像 */}
          <div className={`
            flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center
            ${isUser 
              ? 'bg-blue-100 text-blue-600' 
              : 'bg-green-100 text-green-600'
            }
          `}>
            {isUser ? (
              <User className="h-4 w-4" />
            ) : (
              <Bot className="h-4 w-4" />
            )}
          </div>

          <div className="flex-1 space-y-3">
            {/* 角色标识 */}
            <div className="flex items-center gap-2">
              <Badge variant={isUser ? 'default' : 'secondary'}>
                {isUser ? '用户' : 'AI 助手'}
              </Badge>
              <span className="text-xs text-gray-500">
                {formattedMessage.createdAt.toLocaleString('zh-CN')}
              </span>
            </div>

            {/* 消息内容 */}
            <div className="prose prose-sm max-w-none">
              <MessageContent content={content} />
            </div>

            {/* 文件附件 */}
            {formattedMessage.files && formattedMessage.files.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">附件：</h4>
                <div className="flex flex-wrap gap-2">
                  {formattedMessage.files.map((file, index) => (
                    <FileAttachment key={index} file={file} />
                  ))}
                </div>
              </div>
            )}

            {/* 反馈信息 */}
            {formattedMessage.metadata.feedback && (
              <div className="text-xs text-gray-500 border-t pt-2">
                用户反馈：{formattedMessage.metadata.feedback.rating}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 消息内容渲染组件
 * 直接显示原始数据
 */
function MessageContent({ content }: { content: string }) {
  return (
    <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
      {content}
    </div>
  );
}

/**
 * 文件附件组件
 */
function FileAttachment({ file }: { file: any }) {
  const isImage = file.type?.startsWith('image/') || file.url?.match(/\.(jpg|jpeg|png|gif|webp)$/i);
  
  return (
    <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg border">
      {isImage ? (
        <ImageIcon className="h-4 w-4 text-gray-500" />
      ) : (
        <FileText className="h-4 w-4 text-gray-500" />
      )}
      <span className="text-sm text-gray-700 truncate">
        {file.name || '附件'}
      </span>
      {file.url && (
        <a
          href={file.url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-xs text-blue-600 hover:text-blue-800"
        >
          查看
        </a>
      )}
    </div>
  );
}

/**
 * 对话内容骨架屏
 */
function ConversationContentSkeleton() {
  return (
    <div className="space-y-4">
      <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
      
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className={i % 2 === 0 ? 'ml-8' : 'mr-8'}>
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="h-5 w-16 bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                    <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
