# 提问数据元数据处理方案

## 概述

基于 Dify 工作流的元数据处理方案，通过主工作流流式响应和元数据工作流异步处理的组合，实现用户体验和数据结构化的最佳平衡。

## Dify API 提取元数据工作流密钥
Dify API: http://api2.buyvs.com/v1
API 密钥: app-ZNeqYf8M6TZ7cVPCubWR6YwL
提取元数据使用的是Workflow 应用
配置文件存储在apps\web\.env.local文件中
但是有个问题，在dify api好像使用不同的密钥来区分应用
所以我们需要添加不同的密钥

# 主工作流（当前使用的）原DIFY_API_KEY更改
DIFY_CHAT_API_KEY=app-3vhk2SEBvLEXrgDL8hf6CwVR

# 元数据工作流（新的）
DIFY_METADATA_API_KEY=app-ZNeqYf8M6TZ7cVPCubWR6YwL

## 核心设计理念

### 设计原则
- **用户体验优先**：流式答案立即显示，元数据处理不阻塞用户交互
- **数据完整性**：确保 conversation_id 全流程传递，元数据与会话准确匹配
- **系统可靠性**：异步处理具备容错机制，支持重试和状态跟踪
- **前后端分离**：前端负责内容过滤，后端负责数据处理和存储

### 技术架构
```
用户提问 → 生成短ID → 页面跳转 → 流式显示答案
    ↓           ↓          ↓         ↓
数据库写入 → 主工作流 → 获取会话ID → 元数据工作流 → 数据库更新
```
- 用户提问 → 生成短ID → 页面跳转
- 页面跳转的地址为 `/questions/${shortId}`后面不带[handle]
- 实际上就算只有`/questions/${shortId}`也可以正常访问，如果后面带了[handle]，不会对handle检测是否正确，就算是错的也可以正常访问页面，我们是通过{shortId}，因为后面的[handle]只是做SEO使用。

## 详细流程设计

### 1. 用户提问阶段

#### 1.1 前置处理
```typescript
// 用户提交问题后立即执行
async function handleUserQuestion(question: string, userId?: string) {
  // 1. 生成短ID
  const shortId = await generateUniqueShortId();
  
  // 2. 预写入数据库
  await conversationService.create({
    short_id: shortId,
    user_id: userId,
    is_public: true,
    user_type: userId ? 1 : 0,
    metadata_status: 'pending', // 状态：等待处理
    created_at: new Date()
  });
  
  // 3. 页面跳转
  router.push(`/questions/${shortId}`);
  
  // 4. 启动主工作流
  startMainWorkflow(shortId, question);
}
```

#### 1.2 数据库初始状态
| 字段 | 值 | 说明 |
|------|----|---------|
| short_id | "4m2rzju" | 生成的短ID |
| conversation_id | NULL | 待主工作流返回 |
| metadata_status | "pending" | 等待处理 |
| title | NULL | 待元数据工作流返回 |
| description | NULL | 待元数据工作流返回 |
| handle | NULL | 待元数据工作流返回 |

### 2. 主工作流阶段

#### 2.1 流式响应处理

**处理流程：**
1. 接收前端请求（包含 shortId 和 question）
2. 调用 Dify 主工作流，启动流式聊天
3. 监听流式数据，处理每个数据块：
   - 首次获得 `conversation_id` 时立即更新数据库
   - 获得 `conversation_id` 和 `query` 后立即启动元数据工作流
   把获得的`conversation_id`  传递 到conversation_id 和 `query` 传递到 text
   发送到元数据工作流
   - 将原始数据块转发给前端（前端负责内容过滤）
4. 继续流式响应，元数据处理与主流程并行进行
5. 返回流式响应给前端

**关键方法：**
```typescript
// 立即启动元数据处理
function processMetadataAsync(conversationId: string, query: string) {
  // 不阻塞主流程，异步执行
  setTimeout(() => {
    generateAndSaveMetadata(conversationId, query);
  }, 0);
}
```

**技术要点：**
- **会话用户ID生成**：使用系统内部的用户ID生成算法，为每个对话生成唯一标识符，传递给 Dify API 作为 `user` 参数
```

#### 2.2 会话ID更新
```typescript
// 获得 conversation_id 后立即更新
async function updateConversationId(shortId: string, conversationId: string) {
  await conversationService.update(shortId, {
    conversation_id: conversationId,
    conversation_user: generateSessionUser(),
    metadata_status: 'processing' // 状态：处理中
  });
}
```

### 3. 元数据工作流阶段

#### 3.1 异步元数据处理

**处理流程：**
1. 获得 `conversation_id` 和 `query` 后立即启动
2. 调用 Dify 元数据工作流，传入会话ID和用户问题
3. 元数据工作流基于问题内容生成结构化元数据
4. 验证返回数据的 `conversation_id` 一致性
5. 将元数据保存到数据库，更新状态为 `completed`

**关键方法：**
```typescript
// 元数据生成和保存
async function generateAndSaveMetadata(conversationId: string, query: string) {
  try {
    // 调用 Dify 元数据工作流
    const metadataResponse = await difyService.generateMetadata({
      conversation_id: conversationId,
      query: query
    });
    
    // 验证返回数据格式
    if (metadataResponse.conversation_id === conversationId) {
      await saveMetadataToDatabase(conversationId, metadataResponse.structured_output);
    }
    
  } catch (error) {
    console.error('Metadata processing failed:', error);
    // 标记失败状态，支持后续重试
    await markMetadataFailed(conversationId, error.message);
  }
}
```

#### 3.2 元数据保存
```typescript
// 保存元数据到数据库
async function saveMetadataToDatabase(conversationId: string, metadata: any) {
  await conversationService.updateByConversationId(conversationId, {
    title: metadata.title,
    description: metadata.description,
    handle: metadata.handle,
    language: metadata.language,
    category_id: parseInt(metadata.category),
    metadata_status: 'completed', // 状态：已完成
    updated_at: new Date()
  });
}
```

### 4. 前端流式显示

#### 4.1 流式内容处理

**处理流程：**
1. 接收主工作流的流式数据
2. 直接显示 `answer` 字段的内容（无需过滤和清洗）
3. 使用 Markdown 渲染器显示格式化内容
4. 实时更新用户界面，提供流畅的阅读体验

**技术要点：**
- **纯净内容**：主工作流只返回纯粹的答案内容，不包含元数据
- **直接显示**：前端无需复杂的过滤逻辑，直接显示 `answer` 字段
- **Markdown 支持**：使用 ReactMarkdown 等组件渲染格式化内容
- **流式体验**：实时显示流式数据，提供流畅的用户体验

## 数据结构设计

### conversations_index 表状态变化

#### 阶段1：用户提问后
```sql
INSERT INTO conversations_index (
  short_id, user_id, is_public, user_type, metadata_status, created_at
) VALUES (
  '4m2rzju', 'user-uuid', true, 1, 'pending', NOW()
);
```

#### 阶段2：获得会话ID后
```sql
UPDATE conversations_index 
SET conversation_id = 'b033f7e2-aad2-4773-83c3-3739ec1cb8e6',
    conversation_user = 'user-id',
    metadata_status = 'processing'
WHERE short_id = '4m2rzju';
```

#### 阶段3：元数据完成后
```sql
UPDATE conversations_index 
SET title = '简单的加法计算示例',
    description = '解释了1加2的计算过程并得出结果3。',
    handle = 'simple-addition-example',
    language = 'zh',
    category_id = 1,
    metadata_status = 'completed',
    updated_at = NOW()
WHERE conversation_id = 'b033f7e2-aad2-4773-83c3-3739ec1cb8e6';
```

### 元数据工作流返回格式
```json
{
  "conversation_id": "b033f7e2-aad2-4773-83c3-3739ec1cb8e6",
  "structured_output": {
    "title": "简单的加法计算示例",
    "description": "解释了1加2的计算过程并得出结果3。",
    "handle": "simple-addition-example",
    "language": "zh",
    "category": "1"
  }
}
```

## 技术实现要点

### 1. 会话ID传递机制
- **全流程追踪**：从主工作流到元数据工作流的ID一致性
- **数据库关联**：通过 conversation_id 准确匹配记录
- **错误处理**：ID不匹配时的异常处理机制

### 2. 流式内容处理
- **实时过滤**：前端实时移除答案中的元数据JSON
- **内容累积**：后端累积完整答案用于元数据生成
- **状态同步**：流开始、进行中、结束的状态管理

### 3. 异步任务管理
- **幂等性**：重复执行不会产生副作用
- **重试机制**：失败任务的自动重试策略
- **状态跟踪**：pending → processing → completed/failed

### 4. 错误处理策略
- **主工作流失败**：标记状态，允许重新处理
- **元数据工作流失败**：保留原始数据，支持后续重试
- **网络异常**：超时处理和重连机制

### 5. 性能优化
- **数据库索引**：conversation_id, short_id 的高效查询
- **缓存策略**：元数据生成结果的缓存
- **批量处理**：失败任务的批量重试

## 监控和运维

### 1. 状态监控
```sql
-- 监控各状态的数量
SELECT metadata_status, COUNT(*) as count 
FROM conversations_index 
GROUP BY metadata_status;

-- 查找长时间处理中的任务
SELECT * FROM conversations_index 
WHERE metadata_status = 'processing' 
AND created_at < NOW() - INTERVAL '10 minutes';
```

### 2. 重试机制
```typescript
// 重试失败的元数据处理
async function retryFailedMetadata() {
  const failedTasks = await getFailedMetadataTasks();
  
  for (const task of failedTasks) {
    try {
      await processMetadataAsync(
        task.conversation_id,
        task.cached_query,
        task.cached_answer
      );
    } catch (error) {
      console.error(`Retry failed for ${task.conversation_id}:`, error);
    }
  }
}
```

### 3. 数据完整性检查
```sql
-- 检查缺失元数据的记录
SELECT * FROM conversations_index 
WHERE conversation_id IS NOT NULL 
AND (title IS NULL OR description IS NULL)
AND metadata_status != 'failed';
```

## 总结

这个方案通过精心设计的流程，实现了：

1. **优秀的用户体验**：流式答案立即显示，无需等待元数据
2. **可靠的数据处理**：通过状态管理和重试机制确保数据完整性
3. **清晰的架构设计**：前后端职责分离，易于维护和扩展
4. **强大的容错能力**：多层次的错误处理和恢复机制

该方案既满足了实时性要求，又保证了数据的结构化和完整性，为后续的搜索、分类、推荐等功能奠定了坚实的基础。