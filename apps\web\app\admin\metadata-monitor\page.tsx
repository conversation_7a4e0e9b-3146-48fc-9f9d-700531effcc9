'use client';

import { useState, useEffect } from 'react';
import { <PERSON>f<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle, Clock, Play, Trash2 } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Alert, AlertDescription } from '@kit/ui/alert';

interface MonitorData {
  summary: {
    failed_count: number;
    processing_count: number;
    stuck_count: number;
    total_issues: number;
  };
  failed_tasks: Array<{
    short_id: string;
    title: string;
    retry_count: number;
    updated_at: string;
  }>;
  processing_tasks: Array<{
    short_id: string;
    title: string;
    updated_at: string;
  }>;
  stuck_tasks: Array<{
    short_id: string;
    title: string;
    duration_minutes: number;
    updated_at: string;
  }>;
}

export default function MetadataMonitorPage() {
  const [data, setData] = useState<MonitorData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/metadata-monitor?action=overview');
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const executeAction = async (action: string, shortId?: string) => {
    try {
      setActionLoading(action + (shortId || ''));
      
      const response = await fetch('/api/admin/metadata-monitor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, shortId })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // 刷新数据
        await fetchData();
        alert(`操作成功: ${action}`);
      } else {
        alert(`操作失败: ${result.error}`);
      }
    } catch (err) {
      alert(`操作失败: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchData();
    
    // 每30秒自动刷新
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading && !data) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={fetchData} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">元数据监控管理</h1>
        <Button onClick={fetchData} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </Button>
      </div>

      {/* 概览卡片 */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">失败任务</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {data.summary.failed_count}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">处理中任务</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {data.summary.processing_count}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">卡住任务</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {data.summary.stuck_count}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">总问题数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">
                {data.summary.total_issues}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 批量操作 */}
      {data && data.summary.total_issues > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>批量操作</CardTitle>
          </CardHeader>
          <CardContent className="space-x-2">
            <Button 
              onClick={() => executeAction('retry_all')}
              disabled={actionLoading === 'retry_all'}
              variant="outline"
            >
              <Play className="h-4 w-4 mr-2" />
              重试所有失败任务
            </Button>
            
            <Button 
              onClick={() => executeAction('reset_stuck')}
              disabled={actionLoading === 'reset_stuck'}
              variant="outline"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重置卡住任务
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 失败任务列表 */}
      {data && data.failed_tasks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <XCircle className="h-5 w-5 mr-2 text-red-500" />
              失败任务 ({data.failed_tasks.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.failed_tasks.map((task) => (
                <div key={task.short_id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{task.title}</div>
                    <div className="text-sm text-gray-500">
                      ID: {task.short_id} | 重试次数: {task.retry_count} | 
                      更新时间: {new Date(task.updated_at).toLocaleString()}
                    </div>
                  </div>
                  <div className="space-x-2">
                    <Button
                      size="sm"
                      onClick={() => executeAction('retry_single', task.short_id)}
                      disabled={actionLoading === `retry_single${task.short_id}`}
                    >
                      <Play className="h-3 w-3 mr-1" />
                      重试
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 卡住任务列表 */}
      {data && data.stuck_tasks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-yellow-500" />
              卡住任务 ({data.stuck_tasks.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.stuck_tasks.map((task) => (
                <div key={task.short_id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{task.title}</div>
                    <div className="text-sm text-gray-500">
                      ID: {task.short_id} | 处理时长: {task.duration_minutes} 分钟 | 
                      更新时间: {new Date(task.updated_at).toLocaleString()}
                    </div>
                  </div>
                  <Badge variant="outline" className="text-yellow-600">
                    卡住 {task.duration_minutes}m
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 处理中任务列表 */}
      {data && data.processing_tasks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <RefreshCw className="h-5 w-5 mr-2 text-blue-500 animate-spin" />
              处理中任务 ({data.processing_tasks.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.processing_tasks.map((task) => (
                <div key={task.short_id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{task.title}</div>
                    <div className="text-sm text-gray-500">
                      ID: {task.short_id} | 
                      更新时间: {new Date(task.updated_at).toLocaleString()}
                    </div>
                  </div>
                  <Badge variant="outline" className="text-blue-600">
                    处理中
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 无问题状态 */}
      {data && data.summary.total_issues === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              所有任务正常
            </h3>
            <p className="text-gray-600">
              当前没有失败或卡住的元数据处理任务。
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
