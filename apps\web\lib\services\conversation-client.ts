/**
 * 前端会话服务客户端
 * 封装所有会话相关的 API 调用，提供类型安全的数据获取
 */

import {
  ConversationIndexRow,
  SaveConversationRequest,
  SaveConversationResponse,
  ConversationListResponse,
  ConversationQueryParams,
  UpdateConversationRequest,
  ApiErrorResponse,
} from '../types/conversations';

/**
 * API 响应的通用类型
 */
type ApiResponse<T> = {
  success: true;
  data: T;
  message?: string;
} | {
  success: false;
  error: string;
  details?: string;
  code: string;
};

/**
 * HTTP 请求配置
 */
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
}

/**
 * 会话客户端类
 * 提供所有会话相关的 API 调用方法
 */
export class ConversationClient {
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  /**
   * 发送 HTTP 请求的通用方法
   */
  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const { method = 'GET', headers = {}, body } = config;

    const url = `${this.baseUrl}/api${endpoint}`;
    
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers,
    };

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Request failed',
          details: data.details,
          code: data.code || 'UNKNOWN_ERROR',
        };
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR',
      };
    }
  }

  /**
   * 保存会话
   */
  async saveConversation(
    data: SaveConversationRequest
  ): Promise<ApiResponse<ConversationIndexRow>> {
    return this.request<ConversationIndexRow>('/conversations/save', {
      method: 'POST',
      body: data,
    });
  }

  /**
   * 获取会话列表
   */
  async getConversations(
    params: Partial<ConversationQueryParams> = {}
  ): Promise<ApiResponse<ConversationListResponse>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/conversations${queryString ? `?${queryString}` : ''}`;

    return this.request<ConversationListResponse>(endpoint);
  }

  /**
   * 获取单个会话详情
   */
  async getConversation(shortId: string): Promise<ApiResponse<ConversationIndexRow>> {
    return this.request<ConversationIndexRow>(`/conversations/${shortId}`);
  }

  /**
   * 更新会话信息
   */
  async updateConversation(
    shortId: string,
    data: UpdateConversationRequest
  ): Promise<ApiResponse<ConversationIndexRow>> {
    return this.request<ConversationIndexRow>(`/conversations/${shortId}`, {
      method: 'PUT',
      body: data,
    });
  }

  /**
   * 点赞会话
   */
  async likeConversation(shortId: string): Promise<ApiResponse<ConversationIndexRow>> {
    return this.request<ConversationIndexRow>(`/conversations/${shortId}/like`, {
      method: 'POST',
    });
  }

  /**
   * 删除会话（软删除）
   */
  async deleteConversation(shortId: string): Promise<ApiResponse<{ message: string }>> {
    return this.request<{ message: string }>(`/conversations/${shortId}`, {
      method: 'DELETE',
    });
  }
}

/**
 * 默认的会话客户端实例
 */
export const conversationClient = new ConversationClient();

/**
 * 错误处理辅助函数
 */
export function isApiError(response: ApiResponse<any>): response is ApiErrorResponse {
  return !response.success;
}

/**
 * 成功响应类型守卫
 */
export function isApiSuccess<T>(
  response: ApiResponse<T>
): response is { success: true; data: T; message?: string } {
  return response.success;
}
