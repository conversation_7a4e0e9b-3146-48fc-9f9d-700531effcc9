import { ChatflowService } from './chatflow/chatflow-service';
import { WorkflowService } from './workflow/workflow-service';

/**
 * DifyServices - 统一的 Dify 服务管理器
 * 管理聊天流服务和工作流服务
 */
export class DifyServices {
  private chatflowService: ChatflowService;
  private workflowService: WorkflowService;
  private baseUrl: string;

  constructor(
    chatApiKey: string,
    workflowApiKey: string,
    baseUrl: string = 'https://api2.buyvs.com/v1'
  ) {
    this.baseUrl = baseUrl;
    this.chatflowService = new ChatflowService(chatApiKey, baseUrl);
    this.workflowService = new WorkflowService(workflowApiKey, baseUrl);
  }

  /**
   * 获取聊天流服务
   */
  get chatflow(): ChatflowService {
    return this.chatflowService;
  }

  /**
   * 获取工作流服务
   */
  get workflow(): WorkflowService {
    return this.workflowService;
  }

  /**
   * 更新 API 密钥
   */
  updateApiKeys(chatApiKey: string, workflowApiKey: string): void {
    this.chatflowService.updateApiKey(chatApiKey);
    this.workflowService.updateApiKey(workflowApiKey);
  }
}

// 导出服务类
export { ChatflowService } from './chatflow/chatflow-service';
export { WorkflowService } from './workflow/workflow-service';

// 创建默认实例
let difyServices: DifyServices | null = null;

/**
 * 获取 Dify 服务实例
 */
export function getDifyServices(): DifyServices {
  if (!difyServices) {
    const chatApiKey = process.env.DIFY_CHAT_API_KEY;
    const workflowApiKey = process.env.DIFY_METADATA_API_KEY;
    const baseUrl = process.env.DIFY_BASE_URL || 'https://api2.buyvs.com/v1';

    if (!chatApiKey || !workflowApiKey) {
      throw new Error('Missing required Dify API keys in environment variables');
    }

    difyServices = new DifyServices(chatApiKey, workflowApiKey, baseUrl);
  }

  return difyServices;
}
