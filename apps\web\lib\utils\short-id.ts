/**
 * 简化的短链接 ID 生成工具
 * 使用随机字符串生成 6-8 位的短链接 ID
 */

// 字符集（去除容易混淆的字符：0, O, I, l, 1）
const CHARSET = '23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';

/**
 * 生成随机短链接 ID
 * @param length - 生成的 ID 长度，默认为 8 位
 * @returns 随机生成的短链接 ID
 */
export function generateShortId(length: number = 8): string {
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * CHARSET.length);
    result += CHARSET[randomIndex];
  }
  
  return result;
}

/**
 * 验证短 ID 格式是否有效
 * @param shortId - 要验证的短 ID
 * @returns 是否为有效的短 ID
 */
export function isValidShortId(shortId: string): boolean {
  if (!shortId || shortId.length < 6 || shortId.length > 8) {
    return false;
  }
  
  // 检查是否只包含允许的字符
  for (const char of shortId) {
    if (CHARSET.indexOf(char) === -1) {
      return false;
    }
  }
  
  return true;
}

/**
 * 生成完整的问题 URL
 * @param shortId - 短链接 ID
 * @param handle - URL 友好的标识符
 * @returns 完整的问题 URL 路径
 */
export function generateQuestionUrl(shortId: string, handle: string): string {
  return `/questions/${shortId}/${handle}`;
}

/**
 * 从问题 URL 中提取短 ID
 * @param url - 问题 URL 路径
 * @returns 短 ID，如果解析失败返回 null
 */
export function extractShortIdFromUrl(url: string): string | null {
  const match = url.match(/\/questions\/([^\/]+)/);
  return match && match[1] ? match[1] : null;
}

/**
 * 生成唯一的短链接 ID（带重试机制）
 * @param checkUnique - 检查唯一性的函数
 * @param maxRetries - 最大重试次数，默认为 10
 * @returns Promise<string> 唯一的短链接 ID
 */
export async function generateUniqueShortId(
  checkUnique: (shortId: string) => Promise<boolean>,
  maxRetries: number = 10
): Promise<string> {
  for (let i = 0; i < maxRetries; i++) {
    const shortId = generateShortId();
    const isUnique = await checkUnique(shortId);
    
    if (isUnique) {
      return shortId;
    }
  }
  
  throw new Error('Failed to generate unique short ID after maximum retries');
}
