/*
* global.css
*
* Global styles for the entire application
 */

/* Tailwind CSS */
@import 'tailwindcss';

/* local styles - update the below if you add a new style */
@import './theme.css';
@import './theme.utilities.css';
@import './shadcn-ui.css';
@import './markdoc.css';
@import './makerkit.css';

/* plugins - update the below if you add a new plugin */
@plugin "tailwindcss-animate";

/* content sources - update the below if you add a new path */
@source "../../../packages/*/src/**/*.{ts,tsx}";
@source "../../../packages/features/*/src/**/*.{ts,tsx}";
@source "../../../packages/billing/*/src/**/*.{ts,tsx}";
@source "../../../packages/plugins/*/src/**/*.{ts,tsx}";
@source "../../../packages/cms/*/src/**/*.{ts,tsx}";
@source "../{app,components,config,lib}/**/*.{ts,tsx}";

/* variants - update the below if you add a new variant */
@variant dark (&:where(.dark, .dark *));

@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border, currentColor);
  }

  input::placeholder,
  textarea::placeholder {
    color: theme(--color-muted-foreground);
  }
}