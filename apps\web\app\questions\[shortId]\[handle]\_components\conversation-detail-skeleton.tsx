import { Card, CardContent, CardHeader } from '@kit/ui/card';

/**
 * 会话详情页面骨架屏组件
 */
export function ConversationDetailSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      {/* 返回按钮 */}
      <div className="h-8 w-20 bg-gray-200 rounded" />
      
      {/* 问题信息卡片 */}
      <Card>
        <CardHeader className="space-y-4">
          {/* 标签 */}
          <div className="flex gap-2">
            <div className="h-6 w-16 bg-gray-200 rounded" />
            <div className="h-6 w-20 bg-gray-200 rounded" />
            <div className="h-6 w-12 bg-gray-200 rounded" />
          </div>
          
          {/* 标题 */}
          <div className="space-y-2">
            <div className="h-8 w-3/4 bg-gray-200 rounded" />
            <div className="h-8 w-1/2 bg-gray-200 rounded" />
          </div>
          
          {/* 描述 */}
          <div className="space-y-2">
            <div className="h-4 w-full bg-gray-200 rounded" />
            <div className="h-4 w-5/6 bg-gray-200 rounded" />
            <div className="h-4 w-2/3 bg-gray-200 rounded" />
          </div>
          
          {/* 统计信息 */}
          <div className="flex gap-6">
            <div className="h-4 w-16 bg-gray-200 rounded" />
            <div className="h-4 w-16 bg-gray-200 rounded" />
            <div className="h-4 w-20 bg-gray-200 rounded" />
          </div>
        </CardHeader>
        
        <CardContent>
          {/* 操作按钮 */}
          <div className="flex gap-3">
            <div className="h-8 w-20 bg-gray-200 rounded" />
            <div className="h-8 w-16 bg-gray-200 rounded" />
          </div>
        </CardContent>
      </Card>

      {/* 分隔线 */}
      <div className="h-px bg-gray-200" />

      {/* 对话内容标题 */}
      <div className="h-6 w-32 bg-gray-200 rounded" />

      {/* 对话消息 */}
      <div className="space-y-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className={i % 2 === 0 ? 'ml-8' : 'mr-8'}>
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {/* 头像 */}
                <div className="w-8 h-8 bg-gray-200 rounded-full" />
                
                <div className="flex-1 space-y-3">
                  {/* 角色和时间 */}
                  <div className="flex items-center gap-2">
                    <div className="h-5 w-16 bg-gray-200 rounded" />
                    <div className="h-4 w-24 bg-gray-200 rounded" />
                  </div>
                  
                  {/* 消息内容 */}
                  <div className="space-y-2">
                    <div className="h-4 w-full bg-gray-200 rounded" />
                    <div className="h-4 w-4/5 bg-gray-200 rounded" />
                    <div className="h-4 w-3/5 bg-gray-200 rounded" />
                    {i % 3 === 0 && (
                      <>
                        <div className="h-4 w-full bg-gray-200 rounded" />
                        <div className="h-4 w-2/3 bg-gray-200 rounded" />
                      </>
                    )}
                  </div>
                  
                  {/* 可能的附件 */}
                  {i % 4 === 0 && (
                    <div className="flex gap-2">
                      <div className="h-8 w-20 bg-gray-200 rounded" />
                      <div className="h-8 w-24 bg-gray-200 rounded" />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
