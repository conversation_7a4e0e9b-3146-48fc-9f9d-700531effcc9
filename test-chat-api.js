async function testChatAPI() {
  console.log('=== 测试 Dify trace_id 功能 ===');

  // 生成一个测试用的短ID
  const testShortId = 'TEST' + Math.random().toString(36).substring(2, 8).toUpperCase();
  console.log('测试短ID:', testShortId);

  try {
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Trace-Id': testShortId  // 通过 Header 传递 trace_id
      },
      body: JSON.stringify({
        query: 'What is 2+2? Please include the trace_id in your response.',
        inputs: {
          trace_id: testShortId  // 同时通过 body 传递
        }
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    // 检查是否是流式响应
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('✅ 检测到流式响应');

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      let foundTraceId = false;
      let traceIdInAnswer = false;

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('📡 流式响应结束');
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          fullResponse += chunk;

          // 检查是否包含我们的 trace_id
          if (chunk.includes(testShortId)) {
            console.log('🎉 在数据块中找到 trace_id!');
            foundTraceId = true;
          }

          // 解析 SSE 数据
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                // 检查 trace_id 字段
                if (data.trace_id === testShortId) {
                  console.log('🎯 在 JSON 数据中找到匹配的 trace_id:', data.trace_id);
                  foundTraceId = true;
                }

                // 检查答案内容中是否包含 trace_id
                if (data.answer && data.answer.includes(testShortId)) {
                  console.log('📝 在答案内容中找到 trace_id');
                  console.log('答案片段:', data.answer.substring(0, 200));
                  traceIdInAnswer = true;
                }

                // 显示关键数据
                if (data.event === 'message' || data.event === 'message_end') {
                  console.log(`📨 事件: ${data.event}`);
                  if (data.conversation_id) {
                    console.log(`💬 会话ID: ${data.conversation_id}`);
                  }
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }

        console.log('\n=== 测试结果总结 ===');
        console.log('发送的 trace_id:', testShortId);
        console.log('在响应数据中找到 trace_id:', foundTraceId ? '✅ 是' : '❌ 否');
        console.log('在答案内容中找到 trace_id:', traceIdInAnswer ? '✅ 是' : '❌ 否');
        console.log('完整响应长度:', fullResponse.length);

        if (!foundTraceId && !traceIdInAnswer) {
          console.log('\n❌ 未找到 trace_id，显示前500字符的响应内容:');
          console.log(fullResponse.substring(0, 500));
        }

      } finally {
        reader.releaseLock();
      }
    } else {
      // 非流式响应
      const data = await response.json();
      console.log('📄 非流式响应:', JSON.stringify(data, null, 2));

      // 检查非流式响应中的 trace_id
      if (data.trace_id === testShortId) {
        console.log('🎯 在非流式响应中找到匹配的 trace_id');
      }

      if (data.answer && data.answer.includes(testShortId)) {
        console.log('📝 在非流式答案中找到 trace_id');
      }
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testChatAPI();
