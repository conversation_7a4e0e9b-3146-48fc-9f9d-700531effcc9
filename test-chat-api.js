async function testChatAPI() {
  console.log('Testing Chat API...');
  
  try {
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'What is the capital of France?',
        inputs: {}
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    // 检查是否是流式响应
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('Streaming response detected');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            console.log('Stream ended');
            break;
          }
          
          const chunk = decoder.decode(value, { stream: true });
          fullResponse += chunk;
          console.log('Chunk received:', chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''));
        }
        
        console.log('Full response length:', fullResponse.length);
        console.log('First 200 chars:', fullResponse.substring(0, 200));
        
      } finally {
        reader.releaseLock();
      }
    } else {
      // 非流式响应
      const data = await response.json();
      console.log('JSON response:', data);
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testChatAPI();
