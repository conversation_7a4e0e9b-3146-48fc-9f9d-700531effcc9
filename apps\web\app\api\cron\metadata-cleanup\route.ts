import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { MetadataMonitorService } from '~/lib/services/metadata-monitor-service';

/**
 * 定时清理和监控元数据处理任务
 * POST /api/cron/metadata-cleanup
 * 
 * 这个端点可以被 Vercel Cron Jobs 或其他定时任务服务调用
 */
export async function POST(request: NextRequest) {
  try {
    // 验证请求来源（可选，用于安全性）
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    console.log('Starting metadata cleanup cron job...');

    const supabase = getSupabaseServerAdminClient();
    const monitorService = new MetadataMonitorService(supabase, request.nextUrl.origin);

    const results = {
      stuck_tasks_reset: 0,
      failed_tasks_retried: 0,
      errors: [] as string[]
    };

    try {
      // 1. 检查并重置卡住的任务
      console.log('Checking for stuck tasks...');
      const stuckResult = await monitorService.resetStuckTasks();
      results.stuck_tasks_reset = stuckResult.count;
      
      if (stuckResult.count > 0) {
        console.log(`Reset ${stuckResult.count} stuck tasks:`, stuckResult.tasks);
      }
    } catch (error) {
      const errorMsg = `Failed to reset stuck tasks: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      results.errors.push(errorMsg);
    }

    try {
      // 2. 获取失败的任务并进行有限重试
      console.log('Checking for failed tasks to retry...');
      const failedTasks = await monitorService.getFailedTasks();
      
      // 只重试重试次数少于2次的任务
      const tasksToRetry = failedTasks.filter(task => (task.retry_count || 0) < 2);
      
      if (tasksToRetry.length > 0) {
        console.log(`Found ${tasksToRetry.length} tasks eligible for retry`);
        
        // 限制每次最多重试5个任务，避免过载
        const limitedTasks = tasksToRetry.slice(0, 5);
        
        for (const task of limitedTasks) {
          try {
            const retryResult = await monitorService.retryFailedTask(task.short_id);
            if (retryResult.success) {
              results.failed_tasks_retried++;
              console.log(`Successfully retried task: ${task.short_id}`);
            } else {
              console.log(`Failed to retry task ${task.short_id}: ${retryResult.error}`);
            }
            
            // 添加延迟避免过快的请求
            await new Promise(resolve => setTimeout(resolve, 2000));
          } catch (error) {
            const errorMsg = `Error retrying task ${task.short_id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            console.error(errorMsg);
            results.errors.push(errorMsg);
          }
        }
      }
    } catch (error) {
      const errorMsg = `Failed to process failed tasks: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMsg);
      results.errors.push(errorMsg);
    }

    // 3. 记录清理结果
    console.log('Metadata cleanup completed:', results);

    return NextResponse.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        results,
        summary: {
          stuck_tasks_reset: results.stuck_tasks_reset,
          failed_tasks_retried: results.failed_tasks_retried,
          total_errors: results.errors.length
        }
      }
    });

  } catch (error) {
    console.error('Metadata cleanup cron job failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Cron job failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * 获取清理任务的状态信息
 * GET /api/cron/metadata-cleanup
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = getSupabaseServerAdminClient();
    const monitorService = new MetadataMonitorService(supabase);

    const [failedTasks, processingTasks, stuckTasks] = await Promise.all([
      monitorService.getFailedTasks(),
      monitorService.getProcessingTasks(),
      monitorService.checkStuckTasks()
    ]);

    return NextResponse.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        status: {
          failed_tasks: failedTasks.length,
          processing_tasks: processingTasks.length,
          stuck_tasks: stuckTasks.length,
          needs_attention: failedTasks.length + stuckTasks.length > 0
        },
        details: {
          failed_tasks: failedTasks.map(task => ({
            short_id: task.short_id,
            title: task.title,
            retry_count: task.retry_count || 0,
            updated_at: task.updated_at
          })),
          stuck_tasks: stuckTasks.map(task => ({
            short_id: task.short_id,
            title: task.title,
            duration_minutes: task.duration_minutes,
            updated_at: task.updated_at
          }))
        }
      }
    });
  } catch (error) {
    console.error('Failed to get cleanup status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
