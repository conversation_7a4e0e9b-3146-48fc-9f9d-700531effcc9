import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';

import { ConversationDetailPage } from './_components/conversation-detail-page';
import { ConversationDetailSkeleton } from './_components/conversation-detail-skeleton';
import { ConversationService } from '~/lib/services/conversation-service';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getCategoryInfo } from '~/lib/utils/conversation-utils';

interface PageProps {
  params: Promise<{
    shortId: string;
    handle: string;
  }>;
}

/**
 * 生成页面元数据（SEO）
 */
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { shortId } = await params;

  try {
    // 使用服务端的 ConversationService
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);
    const conversation = await conversationService.getConversationByShortId(shortId);

    if (!conversation) {
      return {
        title: '问题不存在 - AI 作业助手',
        description: '您访问的问题不存在或已被删除。',
      };
    }
    const category = getCategoryInfo(conversation.category_id);

    return {
      title: `${conversation.title} - AI 作业助手`,
      description: conversation.description,
      keywords: [
        conversation.title,
        category.name,
        'AI助手',
        '作业帮助',
        '学习辅导',
      ].join(', '),
      openGraph: {
        title: conversation.title,
        description: conversation.description,
        type: 'article',
        publishedTime: conversation.created_at,
        modifiedTime: conversation.updated_at,
        tags: [category.name],
      },
      twitter: {
        card: 'summary_large_image',
        title: conversation.title,
        description: conversation.description,
      },
      alternates: {
        canonical: `/questions/${shortId}/${conversation.handle}`,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: '问题详情 - AI 作业助手',
      description: 'AI 驱动的智能作业问答平台',
    };
  }
}

/**
 * 会话详情页面
 */
export default async function QuestionDetailPage({ params }: PageProps) {
  const { shortId, handle } = await params;

  // 服务端获取会话基本信息进行 SEO 优化
  const supabase = getSupabaseServerAdminClient();
  const conversationService = new ConversationService(supabase);
  const conversation = await conversationService.getConversationByShortId(shortId);

  if (!conversation) {
    notFound();
  }

  // 验证 handle 是否匹配，如果不匹配则重定向到正确的 URL
  if (conversation.handle !== handle) {
    // 在 Next.js 13+ 中，我们可以使用 redirect
    const { redirect } = await import('next/navigation');
    redirect(`/questions/${shortId}/${conversation.handle}`);
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-4xl px-4 py-8">
        <Suspense fallback={<ConversationDetailSkeleton />}>
          <ConversationDetailPage 
            shortId={shortId} 
            initialData={conversation}
          />
        </Suspense>
      </div>
    </div>
  );
}

/**
 * 生成静态参数（可选，用于静态生成）
 */
export async function generateStaticParams() {
  // 在生产环境中，可以预生成一些热门问题的页面
  // 这里返回空数组，使用 ISR（增量静态再生）
  return [];
}

/**
 * 页面配置
 */
export const dynamic = 'force-dynamic'; // 确保页面是动态的
export const revalidate = 300; // 5分钟重新验证
