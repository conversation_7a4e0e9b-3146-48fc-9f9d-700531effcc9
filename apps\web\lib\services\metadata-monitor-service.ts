import { SupabaseClient } from '@supabase/supabase-js';
import { ConversationService } from './conversation-service';
import { MetadataStatus } from '~/lib/types/conversations';

/**
 * 元数据监控服务
 * 负责监控和重试元数据处理任务
 */
export class MetadataMonitorService {
  private conversationService: ConversationService;
  private baseUrl: string;

  constructor(supabase: SupabaseClient, baseUrl: string = '') {
    this.conversationService = new ConversationService(supabase);
    this.baseUrl = baseUrl;
  }

  /**
   * 获取所有需要重试的失败任务
   */
  async getFailedTasks(): Promise<Array<{
    short_id: string;
    conversation_id: string;
    title: string;
    created_at: string;
    updated_at: string;
    retry_count?: number;
  }>> {
    try {
      const { data, error } = await this.conversationService.supabase
        .from('conversations_index')
        .select('short_id, conversation_id, title, created_at, updated_at, metadata_retry_count')
        .eq('metadata_status', MetadataStatus.FAILED)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Failed to get failed tasks:', error);
        return [];
      }

      return data.map(item => ({
        short_id: item.short_id,
        conversation_id: item.conversation_id || '',
        title: item.title || '未知标题',
        created_at: item.created_at,
        updated_at: item.updated_at,
        retry_count: item.metadata_retry_count || 0
      }));
    } catch (error) {
      console.error('Error getting failed tasks:', error);
      return [];
    }
  }

  /**
   * 获取所有处理中的任务
   */
  async getProcessingTasks(): Promise<Array<{
    short_id: string;
    conversation_id: string;
    title: string;
    created_at: string;
    updated_at: string;
  }>> {
    try {
      const { data, error } = await this.conversationService.supabase
        .from('conversations_index')
        .select('short_id, conversation_id, title, created_at, updated_at')
        .eq('metadata_status', MetadataStatus.PROCESSING)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Failed to get processing tasks:', error);
        return [];
      }

      return data.map(item => ({
        short_id: item.short_id,
        conversation_id: item.conversation_id || '',
        title: item.title || '未知标题',
        created_at: item.created_at,
        updated_at: item.updated_at
      }));
    } catch (error) {
      console.error('Error getting processing tasks:', error);
      return [];
    }
  }

  /**
   * 重试失败的元数据处理任务
   */
  async retryFailedTask(shortId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      console.log(`Retrying metadata processing for ${shortId}`);

      // 获取会话信息
      const conversation = await this.conversationService.getConversationByShortId(shortId);
      if (!conversation) {
        return { success: false, error: 'Conversation not found' };
      }

      // 检查重试次数
      const retryCount = conversation.metadata_retry_count || 0;
      const maxRetries = 3;

      if (retryCount >= maxRetries) {
        return { success: false, error: `Maximum retry attempts (${maxRetries}) exceeded` };
      }

      // 更新重试次数和状态
      await this.conversationService.supabase
        .from('conversations_index')
        .update({
          metadata_status: MetadataStatus.PROCESSING,
          metadata_retry_count: retryCount + 1,
          updated_at: new Date().toISOString()
        })
        .eq('short_id', shortId);

      // 调用元数据处理 API
      const response = await fetch(`${this.baseUrl}/api/chat/metadata`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_id: conversation.conversation_id,
          short_id: shortId,
          query: conversation.title || '重试处理',
          answer: conversation.description || ''
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Retry request failed');
      }

      console.log(`Successfully initiated retry for ${shortId}`);
      return { success: true };

    } catch (error) {
      console.error(`Failed to retry task ${shortId}:`, error);
      
      // 更新状态回到失败
      await this.conversationService.supabase
        .from('conversations_index')
        .update({
          metadata_status: MetadataStatus.FAILED,
          updated_at: new Date().toISOString()
        })
        .eq('short_id', shortId);

      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 批量重试失败的任务
   */
  async retryAllFailedTasks(): Promise<{
    total: number;
    successful: number;
    failed: number;
    results: Array<{
      shortId: string;
      success: boolean;
      error?: string;
    }>;
  }> {
    const failedTasks = await this.getFailedTasks();
    const results = [];
    let successful = 0;
    let failed = 0;

    console.log(`Starting batch retry for ${failedTasks.length} failed tasks`);

    for (const task of failedTasks) {
      const result = await this.retryFailedTask(task.short_id);
      results.push({
        shortId: task.short_id,
        success: result.success,
        error: result.error
      });

      if (result.success) {
        successful++;
      } else {
        failed++;
      }

      // 添加延迟避免过快的请求
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`Batch retry completed: ${successful} successful, ${failed} failed`);

    return {
      total: failedTasks.length,
      successful,
      failed,
      results
    };
  }

  /**
   * 检查长时间处理中的任务（超过10分钟）
   */
  async checkStuckTasks(): Promise<Array<{
    short_id: string;
    title: string;
    updated_at: string;
    duration_minutes: number;
  }>> {
    try {
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();

      const { data, error } = await this.conversationService.supabase
        .from('conversations_index')
        .select('short_id, title, updated_at')
        .eq('metadata_status', MetadataStatus.PROCESSING)
        .lt('updated_at', tenMinutesAgo);

      if (error) {
        console.error('Failed to check stuck tasks:', error);
        return [];
      }

      return data.map(item => ({
        short_id: item.short_id,
        title: item.title || '未知标题',
        updated_at: item.updated_at,
        duration_minutes: Math.round((Date.now() - new Date(item.updated_at).getTime()) / (1000 * 60))
      }));
    } catch (error) {
      console.error('Error checking stuck tasks:', error);
      return [];
    }
  }

  /**
   * 重置卡住的任务状态
   */
  async resetStuckTasks(): Promise<{
    count: number;
    tasks: string[];
  }> {
    const stuckTasks = await this.checkStuckTasks();
    
    if (stuckTasks.length === 0) {
      return { count: 0, tasks: [] };
    }

    const shortIds = stuckTasks.map(task => task.short_id);

    try {
      const { error } = await this.conversationService.supabase
        .from('conversations_index')
        .update({
          metadata_status: MetadataStatus.FAILED,
          updated_at: new Date().toISOString()
        })
        .in('short_id', shortIds);

      if (error) {
        console.error('Failed to reset stuck tasks:', error);
        return { count: 0, tasks: [] };
      }

      console.log(`Reset ${stuckTasks.length} stuck tasks to failed status`);
      return { count: stuckTasks.length, tasks: shortIds };
    } catch (error) {
      console.error('Error resetting stuck tasks:', error);
      return { count: 0, tasks: [] };
    }
  }
}
