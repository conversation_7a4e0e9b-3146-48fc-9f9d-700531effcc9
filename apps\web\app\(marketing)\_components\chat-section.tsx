'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { ChatInput } from './chat-input';
import { AIResponse } from './ai-response';
import { AIThinkingCard } from './loading-states';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: 'image' | 'document';
  mimeType: string;
  url?: string;
}

interface ChatMessage {
  id: string;
  answer: string;
  conversationId: string;
  shortId: string;
  createdAt: number;
}

export function ChatSection() {
  const router = useRouter();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [streamingAnswer, setStreamingAnswer] = useState<string>('');
  const [currentShortId, setCurrentShortId] = useState<string | undefined>();

  const handleSendMessage = useCallback(async (message: string, files: UploadedFile[]): Promise<ChatMessage> => {
    setIsLoading(true);
    setStreamingAnswer('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: message,
          conversationId: currentConversationId,
          files: files.map(file => ({
            type: file.type,
            uploadFileId: file.id,
            name: file.name,
            size: file.size,
            mimeType: file.mimeType,
          })),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error response:', errorData);
        throw new Error(errorData.details || 'Failed to send message');
      }

      // 处理流式响应
      if (!response.body) {
        throw new Error('No response body');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullAnswer = '';
      let finalConversationId = '';
      let finalShortId = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);

                if (parsed.type === 'answer') {
                  fullAnswer += parsed.content;
                  setStreamingAnswer(fullAnswer);
                } else if (parsed.type === 'done') {
                  finalConversationId = parsed.conversation_id;
                  finalShortId = parsed.short_id;
                }
              } catch (e) {
                console.warn('Failed to parse SSE data:', data);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // 创建消息对象
      const chatMessage: ChatMessage = {
        id: finalShortId || Date.now().toString(),
        answer: fullAnswer,
        conversationId: finalConversationId,
        shortId: finalShortId,
        createdAt: Date.now(),
      };

      // 跳转到问题详情页面
      if (finalShortId) {
        router.push(`/questions/${finalShortId}`);
      }

      return chatMessage;
    } catch (error) {
      console.error('Send message error:', error);
      throw error;
    } finally {
      setIsLoading(false);
      setStreamingAnswer('');
    }
  }, [currentConversationId, router]);

  const handleMessageSent = useCallback((message: ChatMessage) => {
    setMessages(prev => [...prev, message]);
    setCurrentConversationId(message.conversationId);
    setCurrentShortId(message.shortId);
  }, []);

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-5xl md:text-6xl font-bold text-foreground">
          <Trans i18nKey="marketing:heroTitle" defaults="#1 Free AI Homework Helper" />
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          <Trans 
            i18nKey="marketing:heroSubtitle" 
            defaults="Study with our AI Homework Helper to get instant, step-by-step solutions to any problem." 
          />
        </p>
      </div>

      {/* Chat Input Area */}
      <div className="max-w-2xl mx-auto">
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <ChatInput 
              onSendMessage={handleSendMessage}
              onMessageSent={handleMessageSent}
            />
          </CardContent>
        </Card>
      </div>

      {/* Messages Display */}
      {(messages.length > 0 || isLoading || streamingAnswer) && (
        <div className="max-w-4xl mx-auto space-y-6">
          {messages.map((message) => (
            <AIResponse
              key={message.id}
              message={message}
              showActions={true}
            />
          ))}

          {/* 流式响应显示 */}
          {streamingAnswer && (
            <AIResponse
              message={{
                id: 'streaming',
                answer: streamingAnswer,
                conversationId: '',
                shortId: '',
                createdAt: Date.now()
              }}
              showActions={false}
              isStreaming={true}
            />
          )}

          {/* Loading State */}
          {isLoading && !streamingAnswer && (
            <AIThinkingCard stage="processing" />
          )}
        </div>
      )}
    </div>
  );
}
