import { NextRequest, NextResponse } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

/**
 * 获取 Dify 对话详情 API
 * GET /api/dify/conversations/[id]
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id: conversationId } = await params;

    if (!conversationId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Conversation ID is required',
          code: 'MISSING_CONVERSATION_ID',
        },
        { status: 400 }
      );
    }

    // 首先从数据库获取会话信息，包括 conversation_user
    const supabase = getSupabaseServerAdminClient();

    // 通过 conversation_id 查找会话记录
    const { data: conversations, error: dbError } = await supabase
      .from('conversations_index')
      .select('conversation_user')
      .eq('conversation_id', conversationId)
      .single();

    if (dbError || !conversations) {
      console.error('Failed to find conversation in database:', dbError);
      return NextResponse.json(
        {
          success: false,
          error: 'Conversation not found in database',
          details: dbError?.message || 'No conversation record found',
          code: 'CONVERSATION_NOT_IN_DB',
        },
        { status: 404 }
      );
    }

    console.log(`Found conversation in database with user: ${conversations.conversation_user}`);

    const difyServices = getDifyServices();

    // 获取对话详情（包含消息），使用数据库中的 conversation_user
    const conversationDetail = await difyServices.chatflow.createChatMessage(
      '',  // 空查询，只获取对话详情
      conversations.conversation_user,
      conversationId
    );

    if (!conversationDetail) {
      return NextResponse.json(
        {
          success: false,
          error: 'Conversation not found in Dify or has no messages',
          code: 'DIFY_CONVERSATION_NOT_FOUND',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: conversationDetail,
    });
  } catch (error) {
    console.error('Dify conversation API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch conversation detail',
        details: error instanceof Error ? error.message : 'Unknown error',
        code: 'FETCH_CONVERSATION_FAILED',
      },
      { status: 500 }
    );
  }
}
