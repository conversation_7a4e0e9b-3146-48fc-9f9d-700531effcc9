import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { ConversationService } from '~/lib/services/conversation-service';
import {
  ApiErrorResponse,
  UpdateConversationRequest,
} from '~/lib/types/conversations';
import { isValidShortId } from '~/lib/utils/short-id';

/**
 * 获取单个会话详情 API
 * GET /api/conversations/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;

    // 验证短 ID 格式
    if (!isValidShortId(id)) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Invalid conversation ID format',
        code: 'INVALID_ID',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 创建会话服务实例
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 获取会话详情
    const conversation = await conversationService.getConversationByShortId(id);

    if (!conversation) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Conversation not found',
        code: 'NOT_FOUND',
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    // 增加浏览量（异步执行，不等待结果）
    conversationService.incrementViewCount(id).catch(error => {
      console.error('Failed to increment view count:', error);
    });

    return NextResponse.json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    console.error('Conversation detail API error:', error);
    
    const errorResponse: ApiErrorResponse = {
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      code: 'INTERNAL_ERROR',
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * 更新会话信息 API
 * PUT /api/conversations/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;
    const body = await request.json();

    // 验证短 ID 格式
    if (!isValidShortId(id)) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Invalid conversation ID format',
        code: 'INVALID_ID',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 验证更新数据
    const updateData: UpdateConversationRequest = {};
    
    if (body.view_count !== undefined && typeof body.view_count === 'number') {
      updateData.view_count = body.view_count;
    }
    
    if (body.like_count !== undefined && typeof body.like_count === 'number') {
      updateData.like_count = body.like_count;
    }
    
    if (body.is_public !== undefined && typeof body.is_public === 'boolean') {
      updateData.is_public = body.is_public;
    }
    
    if (body.title !== undefined && typeof body.title === 'string') {
      updateData.title = body.title;
    }
    
    if (body.description !== undefined && typeof body.description === 'string') {
      updateData.description = body.description;
    }

    // 检查是否有有效的更新数据
    if (Object.keys(updateData).length === 0) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'No valid update data provided',
        code: 'NO_UPDATE_DATA',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 创建会话服务实例
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 更新会话
    const updatedConversation = await conversationService.updateConversation(id, updateData);

    if (!updatedConversation) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Failed to update conversation or conversation not found',
        code: 'UPDATE_FAILED',
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: updatedConversation,
      message: 'Conversation updated successfully',
    });
  } catch (error) {
    console.error('Conversation update API error:', error);
    
    const errorResponse: ApiErrorResponse = {
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      code: 'INTERNAL_ERROR',
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * 删除会话 API（软删除）
 * DELETE /api/conversations/[id]
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;

    // 验证短 ID 格式
    if (!isValidShortId(id)) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Invalid conversation ID format',
        code: 'INVALID_ID',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 创建会话服务实例
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 删除会话（软删除）
    const success = await conversationService.deleteConversation(id);

    if (!success) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Failed to delete conversation or conversation not found',
        code: 'DELETE_FAILED',
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Conversation deleted successfully',
    });
  } catch (error) {
    console.error('Conversation delete API error:', error);
    
    const errorResponse: ApiErrorResponse = {
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      code: 'INTERNAL_ERROR',
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
