{"$schema": "https://turborepo.org/schema.json", "globalDependencies": ["**/.env"], "ui": "stream", "globalEnv": ["STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "NEXT_PUBLIC_PRODUCT_NAME", "NEXT_PUBLIC_SITE_URL", "NEXT_PUBLIC_MONITORING_PROVIDER", "EMAIL_SENDER", "EMAIL_PORT", "EMAIL_HOST", "EMAIL_TLS", "EMAIL_USER", "EMAIL_PASSWORD", "CMS_CLIENT", "LOGGER", "LEMON_SQUEEZY_SECRET_KEY", "LEMON_SQUEEZY_SIGNING_SECRET", "LEMON_SQUEEZY_STORE_ID", "KEYSTATIC_STORAGE_KIND", "KEYSTATIC_STORAGE_PROJECT", "KEYSTATIC_STORAGE_REPO", "KEYSTATIC_STORAGE_BRANCH_PREFIX", "KEYSTATIC_PATH_PREFIX", "KEYSTATIC_GITHUB_TOKEN", "NEXT_PUBLIC_KEYSTATIC_STORAGE_REPO", "NEXT_PUBLIC_KEYSTATIC_CONTENT_PATH", "NEXT_PUBLIC_KEYSTATIC_STORAGE_KIND", "WORDPRESS_API_URL", "SUPABASE_DB_WEBHOOK_SECRET", "INSTRUMENTATION_SERVICE_NAME", "NEXT_PUBLIC_SENTRY_DSN", "CAPTCHA_SECRET_TOKEN", "NEXT_PUBLIC_BASELIME_KEY", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "ENABLE_BILLING_TESTS", "RESEND_API_KEY", "MAILER_PROVIDER", "CONTACT_EMAIL"], "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "next-env.d.ts"]}, "dev": {"persistent": true, "cache": false}, "format": {"outputs": ["node_modules/.cache/.prettiercache"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^topo"], "outputs": ["node_modules/.cache/.eslintcache"]}, "typecheck": {"dependsOn": ["^topo"], "outputs": ["node_modules/.cache/tsbuildinfo.json"]}, "test": {"dependsOn": ["^topo"]}, "clean": {"cache": false}, "//#clean": {"cache": false}, "license#dev": {"outputLogs": "errors-only"}}}