'use client';

import { useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  RefreshCw,
  Wifi,
  WifiOff
} from 'lucide-react';

import {
  StreamingAnswerProps,
  StreamEvent,
  StreamEventType
} from '~/lib/types/components';
import { AnswerStatus } from '~/lib/types/conversations';
import { useStreamingAnswerState, useSSEConnection, createComponentError } from '~/lib/hooks/use-component-state';

/**
 * StreamingAnswer 组件
 * 功能：实时显示正在生成的答案内容
 * 输入：短ID或会话ID
 * 状态管理：监控 answer_status (pending/streaming)
 * 独立性：可独立建立SSE连接，处理流式数据
 */
export function StreamingAnswer({
  shortId,
  conversationId,
  onComplete,
  onError,
  onStatusChange,
  autoStart = true,
  timeout = 300000, // 5分钟
  pollInterval = 2000, // 2秒
  className = '',
  showStatusIndicator = true,
  showProgress = true
}: StreamingAnswerProps) {
  const { state, actions } = useStreamingAnswerState(shortId);

  // SSE连接处理
  const handleSSEMessage = useCallback((event: StreamEvent) => {
    actions.updateLastUpdate();

    switch (event.type) {
      case StreamEventType.STATUS:
        if (event.answerStatus) {
          actions.setAnswerStatus(event.answerStatus);
          onStatusChange?.(event.answerStatus);
        }
        if (event.conversationId) {
          actions.setConversationId(event.conversationId);
        }
        break;

      case StreamEventType.STATUS_UPDATE:
        if (event.answerStatus) {
          actions.setAnswerStatus(event.answerStatus);
          onStatusChange?.(event.answerStatus);
        }
        if (event.conversationId) {
          actions.setConversationId(event.conversationId);
        }
        if (event.pollCount) {
          actions.incrementPollCount();
        }
        break;

      case StreamEventType.COMPLETE:
        if (event.content) {
          actions.setContent(event.content);
          actions.setAnswerStatus(AnswerStatus.COMPLETED);
          onStatusChange?.(AnswerStatus.COMPLETED);
          onComplete?.(event.content, event.conversationId || '');
        }
        break;

      case StreamEventType.ERROR:
        const error = createComponentError(
          'STREAMING_ERROR',
          event.message || 'Streaming error occurred'
        );
        actions.setError(error);
        onError?.(error);
        break;

      case StreamEventType.TIMEOUT:
        const timeoutError = createComponentError(
          'TIMEOUT_ERROR',
          event.message || 'Streaming timeout'
        );
        actions.setError(timeoutError);
        onError?.(timeoutError);
        break;
    }
  }, [actions, onComplete, onError, onStatusChange]);

  const handleSSEError = useCallback((error: any) => {
    actions.setError(error);
    onError?.(error);
  }, [actions, onError]);

  const handleSSEOpen = useCallback(() => {
    actions.setConnected(true);
  }, [actions]);

  const handleSSEClose = useCallback(() => {
    actions.setConnected(false);
  }, [actions]);

  // SSE连接
  const { isConnected, connect, disconnect } = useSSEConnection(
    `/api/streaming-answer?shortId=${shortId}`,
    {
      autoStart,
      onMessage: handleSSEMessage,
      onError: handleSSEError,
      onOpen: handleSSEOpen,
      onClose: handleSSEClose
    }
  );

  // 手动重连
  const handleReconnect = useCallback(() => {
    actions.reset();
    connect();
  }, [actions, connect]);

  // 超时处理
  useEffect(() => {
    if (!isConnected) return;

    const timeoutId = setTimeout(() => {
      const timeoutError = createComponentError(
        'CONNECTION_TIMEOUT',
        `连接超时 (${timeout / 1000}秒)`
      );
      actions.setError(timeoutError);
      onError?.(timeoutError);
      disconnect();
    }, timeout);

    return () => clearTimeout(timeoutId);
  }, [isConnected, timeout, actions, onError, disconnect]);

  // 获取状态配置
  const getStatusConfig = (status: AnswerStatus) => {
    switch (status) {
      case AnswerStatus.PENDING:
        return {
          icon: Clock,
          text: '等待处理',
          color: 'bg-yellow-50 text-yellow-700 border-yellow-200',
          description: 'AI 正在准备回答您的问题'
        };
      case AnswerStatus.STREAMING:
        return {
          icon: Loader2,
          text: '正在生成',
          color: 'bg-blue-50 text-blue-700 border-blue-200',
          description: 'AI 正在为您生成详细的回答'
        };
      case AnswerStatus.COMPLETED:
        return {
          icon: CheckCircle,
          text: '已完成',
          color: 'bg-green-50 text-green-700 border-green-200',
          description: '回答已生成完成'
        };
      case AnswerStatus.FAILED:
        return {
          icon: AlertCircle,
          text: '生成失败',
          color: 'bg-red-50 text-red-700 border-red-200',
          description: '回答生成过程中出现了问题'
        };
      default:
        return {
          icon: Clock,
          text: '未知状态',
          color: 'bg-gray-50 text-gray-700 border-gray-200',
          description: '状态未知'
        };
    }
  };

  const statusConfig = getStatusConfig(state.answerStatus);
  const StatusIcon = statusConfig.icon;

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">AI 回答</CardTitle>
          
          {showStatusIndicator && (
            <div className="flex items-center gap-2">
              {/* 连接状态指示器 */}
              <div className="flex items-center gap-1">
                {isConnected ? (
                  <Wifi className="h-4 w-4 text-green-600" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-600" />
                )}
                <span className="text-xs text-muted-foreground">
                  {isConnected ? '已连接' : '未连接'}
                </span>
              </div>

              {/* 状态徽章 */}
              <Badge variant="outline" className={statusConfig.color}>
                <StatusIcon className={`h-3 w-3 mr-1 ${
                  state.answerStatus === AnswerStatus.STREAMING ? 'animate-spin' : ''
                }`} />
                {statusConfig.text}
              </Badge>
            </div>
          )}
        </div>

        {showProgress && (
          <div className="text-sm text-muted-foreground">
            {statusConfig.description}
            {state.pollCount > 0 && (
              <span className="ml-2">
                (检查次数: {state.pollCount})
              </span>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent>
        {/* 错误状态 */}
        {state.error && (
          <div className="space-y-4">
            <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-destructive">
                    {state.error.message}
                  </h4>
                  {state.error.details && (
                    <p className="text-sm text-destructive/80 mt-1">
                      {state.error.details}
                    </p>
                  )}
                  <p className="text-xs text-destructive/60 mt-2">
                    错误代码: {state.error.code}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                onClick={handleReconnect}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                重新连接
              </Button>
            </div>
          </div>
        )}

        {/* 加载状态 */}
        {!state.error && (state.answerStatus === AnswerStatus.PENDING || state.answerStatus === AnswerStatus.STREAMING) && (
          <div className="space-y-4">
            <div className="flex flex-col items-center justify-center py-8">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                {state.answerStatus === AnswerStatus.STREAMING && (
                  <div className="absolute -inset-2 border-2 border-primary/20 rounded-full animate-pulse" />
                )}
              </div>
              
              <div className="text-center mt-4">
                <h3 className="font-medium">
                  {state.answerStatus === AnswerStatus.PENDING ? '准备中...' : '生成中...'}
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {statusConfig.description}
                </p>
              </div>

              {/* 进度指示器 */}
              {showProgress && state.pollCount > 0 && (
                <div className="mt-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                    <span>已检查 {state.pollCount} 次</span>
                  </div>
                </div>
              )}
            </div>

            {/* 连接信息 */}
            {showProgress && (
              <div className="text-xs text-muted-foreground space-y-1 p-3 bg-muted/50 rounded-md">
                <div>短ID: {shortId}</div>
                {state.conversationId && (
                  <div>会话ID: {state.conversationId}</div>
                )}
                {state.lastUpdate && (
                  <div>最后更新: {new Date(state.lastUpdate).toLocaleTimeString()}</div>
                )}
              </div>
            )}
          </div>
        )}

        {/* 完成状态 */}
        {!state.error && state.answerStatus === AnswerStatus.COMPLETED && state.content && (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">回答生成完成</span>
            </div>
            
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap">{state.content}</div>
            </div>

            {showProgress && (
              <div className="text-xs text-muted-foreground p-3 bg-green-50 rounded-md">
                <div>✅ 回答已完成</div>
                <div>📝 内容长度: {state.content.length} 字符</div>
                {state.conversationId && (
                  <div>🔗 会话ID: {state.conversationId}</div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
