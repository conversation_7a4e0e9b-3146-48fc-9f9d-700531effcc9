'use client';

import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Copy, Check, ThumbsUp, ThumbsDown, Share2 } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

// 注意：这些样式需要在全局 CSS 中添加，而不是在组件中导入
// 暂时移除 CSS 导入以修复构建错误

interface ChatMessage {
  id: string;
  answer: string;
  conversationId: string;
  shortId: string;
  createdAt: number;
  metadata?: {
    usage?: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
  };
}

interface AIResponseProps {
  message: ChatMessage;
  className?: string;
  showActions?: boolean;
  isStreaming?: boolean;
}

export function AIResponse({ message, className, showActions = true, isStreaming = false }: AIResponseProps) {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<'like' | 'dislike' | null>(null);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.answer);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const handleFeedback = (type: 'like' | 'dislike') => {
    setFeedback(type);
    // TODO: 发送反馈到后端
    console.log('Feedback:', type, 'for message:', message.id);
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'StudyX AI Answer',
          text: message.answer,
          url: window.location.href,
        });
      } else {
        // 降级到复制链接
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Failed to share:', error);
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">AI</span>
            </div>
            <span>StudyX AI</span>
            {isStreaming && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600">正在回答...</span>
              </div>
            )}
          </CardTitle>
          <Badge variant="secondary" className="text-xs">
            {new Date(message.createdAt * 1000).toLocaleString()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* AI 回答内容 */}
        <div className="prose prose-sm max-w-none dark:prose-invert">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              // 自定义代码块样式
              code: ({ className, children, ...props }) => {
                const match = /language-(\w+)/.exec(className || '');
                const isCodeBlock = match && className?.includes('language-');

                return isCodeBlock ? (
                  <div className="relative">
                    <pre className={className}>
                      <code {...props}>{children}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2 h-8 w-8 p-0"
                      onClick={() => navigator.clipboard.writeText(String(children))}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                ) : (
                  <code className={className} {...props}>
                    {children}
                  </code>
                );
              },
              // 自定义表格样式
              table: ({ children }) => (
                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse border border-border">
                    {children}
                  </table>
                </div>
              ),
              th: ({ children }) => (
                <th className="border border-border bg-muted p-2 text-left font-semibold">
                  {children}
                </th>
              ),
              td: ({ children }) => (
                <td className="border border-border p-2">{children}</td>
              ),
              // 自定义链接样式
              a: ({ href, children }) => (
                <a
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {children}
                </a>
              ),
            }}
          >
            {message.answer}
          </ReactMarkdown>
        </div>

        {/* 元数据信息 */}
        {message.metadata?.usage && (
          <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
            <div className="flex items-center space-x-4">
              <span>
                <Trans i18nKey="chat:tokens" defaults="Tokens used" />: {message.metadata.usage.totalTokens}
              </span>
              <span>•</span>
              <span>
                <Trans i18nKey="chat:prompt" defaults="Prompt" />: {message.metadata.usage.promptTokens}
              </span>
              <span>•</span>
              <span>
                <Trans i18nKey="chat:completion" defaults="Completion" />: {message.metadata.usage.completionTokens}
              </span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {showActions && (
          <div className="flex items-center justify-between pt-4 border-t border-border">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFeedback('like')}
                className={cn(
                  "h-8 px-3",
                  feedback === 'like' && "bg-green-100 text-green-700 dark:bg-green-900/20"
                )}
              >
                <ThumbsUp className="w-4 h-4 mr-1" />
                <Trans i18nKey="chat:helpful" defaults="Helpful" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFeedback('dislike')}
                className={cn(
                  "h-8 px-3",
                  feedback === 'dislike' && "bg-red-100 text-red-700 dark:bg-red-900/20"
                )}
              >
                <ThumbsDown className="w-4 h-4 mr-1" />
                <Trans i18nKey="chat:notHelpful" defaults="Not helpful" />
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className="h-8 px-3"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4 mr-1" />
                    <Trans i18nKey="chat:copied" defaults="Copied" />
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 mr-1" />
                    <Trans i18nKey="chat:copy" defaults="Copy" />
                  </>
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="h-8 px-3"
              >
                <Share2 className="w-4 h-4 mr-1" />
                <Trans i18nKey="chat:share" defaults="Share" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
