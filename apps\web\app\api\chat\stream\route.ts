import { NextRequest } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';
import {
  ChatRequest,
  DifyApiError,
  generateUserId
} from '~/lib/dify/types';

/**
 * 流式聊天 API Route
 * POST /api/chat/stream
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body: ChatRequest = await request.json();
    const { query, conversationId, files, inputs } = body;

    // 验证必需参数
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return new Response(
        JSON.stringify({ error: 'Query is required and cannot be empty' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // 获取或生成用户 ID
    const userId = generateUserId();

    // 获取 Dify 服务实例
    const difyServices = getDifyServices();

    // 准备文件参数
    const difyFiles = files?.map(file => ({
      type: file.type,
      transfer_method: file.uploadFileId ? 'local_file' : 'remote_url',
      url: file.url,
      upload_file_id: file.uploadFileId,
    })) || [];

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 调用 Dify 流式 API
          const responseStream = difyServices.chatflow.chatStream(
            query.trim(),
            userId,
            conversationId,
            inputs || {}
          );

          // 处理流式数据
          for await (const chunk of responseStream) {
            const data = JSON.stringify(chunk);
            controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`));
          }

          // 发送结束信号
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'));
          controller.close();

        } catch (error) {
          console.error('Stream error:', error);
          
          const errorData = {
            event: 'error',
            data: {
              error: error instanceof DifyApiError ? error.message : 'Stream processing failed',
              code: error instanceof DifyApiError ? error.code : 'STREAM_ERROR'
            }
          };
          
          controller.enqueue(
            new TextEncoder().encode(`data: ${JSON.stringify(errorData)}\n\n`)
          );
          controller.close();
        }
      }
    });

    // 返回 SSE 响应
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Stream chat API error:', error);

    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: 'Failed to initialize stream. Please try again later.'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * 处理 OPTIONS 请求（CORS 预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
