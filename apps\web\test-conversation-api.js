/**
 * 会话 API 测试脚本
 * 测试所有会话相关的 API 端点
 */

const BASE_URL = 'http://localhost:3000';

// 生成一个有效的 UUID v4
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 验证 UUID 格式
function isValidUUID(uuid) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// 生成测试数据
function createTestConversation() {
  const uuid = generateUUID();
  console.log('Generated UUID:', uuid);
  console.log('UUID valid:', isValidUUID(uuid));

  return {
    conversation_id: uuid,
    structured_output: {
      title: '测试问题：如何学习 JavaScript？',
      description: '这是一个关于 JavaScript 学习方法的问题，包含了基础语法、进阶概念和实践项目的建议。',
      handle: 'how-to-learn-javascript',
      language: 'zh',
      category: 10, // 计算机
    },
    user_id: null,
    user_type: 0,
    is_public: true,
  };
}

/**
 * 发送 HTTP 请求的辅助函数
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data,
    };
  } catch (error) {
    console.error('Request failed:', error);
    return {
      status: 0,
      ok: false,
      error: error.message,
    };
  }
}

/**
 * 测试保存会话 API
 */
async function testSaveConversation() {
  console.log('\n=== 测试保存会话 API ===');

  const testConversation = createTestConversation();

  const result = await makeRequest(`${BASE_URL}/api/conversations/save`, {
    method: 'POST',
    body: JSON.stringify(testConversation),
  });

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  if (result.ok && result.data.success) {
    console.log('✅ 保存会话成功');
    return result.data.data; // 返回保存的会话数据
  } else {
    console.log('❌ 保存会话失败');
    return null;
  }
}

/**
 * 测试获取会话列表 API
 */
async function testGetConversations() {
  console.log('\n=== 测试获取会话列表 API ===');
  
  const params = new URLSearchParams({
    page: '1',
    limit: '10',
    is_public: 'true',
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  const result = await makeRequest(`${BASE_URL}/api/conversations?${params}`);

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  if (result.ok && result.data.success) {
    console.log('✅ 获取会话列表成功');
    console.log(`📊 共找到 ${result.data.data.length} 条会话记录`);
    return result.data.data;
  } else {
    console.log('❌ 获取会话列表失败');
    return [];
  }
}

/**
 * 测试获取单个会话 API
 */
async function testGetConversation(shortId) {
  console.log('\n=== 测试获取单个会话 API ===');
  console.log('Short ID:', shortId);
  
  const result = await makeRequest(`${BASE_URL}/api/conversations/${shortId}`);

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  if (result.ok && result.data.success) {
    console.log('✅ 获取会话详情成功');
    return result.data.data;
  } else {
    console.log('❌ 获取会话详情失败');
    return null;
  }
}

/**
 * 测试点赞会话 API
 */
async function testLikeConversation(shortId) {
  console.log('\n=== 测试点赞会话 API ===');
  console.log('Short ID:', shortId);
  
  const result = await makeRequest(`${BASE_URL}/api/conversations/${shortId}/like`, {
    method: 'POST',
  });

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  if (result.ok && result.data.success) {
    console.log('✅ 点赞会话成功');
    return result.data.data;
  } else {
    console.log('❌ 点赞会话失败');
    return null;
  }
}

/**
 * 测试更新会话 API
 */
async function testUpdateConversation(shortId) {
  console.log('\n=== 测试更新会话 API ===');
  console.log('Short ID:', shortId);
  
  const updateData = {
    title: '更新后的标题：JavaScript 学习指南',
    description: '这是更新后的描述，包含了更详细的学习路径和资源推荐。',
  };

  const result = await makeRequest(`${BASE_URL}/api/conversations/${shortId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData),
  });

  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data, null, 2));

  if (result.ok && result.data.success) {
    console.log('✅ 更新会话成功');
    return result.data.data;
  } else {
    console.log('❌ 更新会话失败');
    return null;
  }
}

/**
 * 测试无效参数的情况
 */
async function testInvalidRequests() {
  console.log('\n=== 测试无效参数处理 ===');
  
  // 测试无效的保存请求
  console.log('\n--- 测试无效的保存请求 ---');
  const invalidSaveResult = await makeRequest(`${BASE_URL}/api/conversations/save`, {
    method: 'POST',
    body: JSON.stringify({ invalid: 'data' }),
  });
  console.log('Invalid save status:', invalidSaveResult.status);
  console.log('Invalid save response:', JSON.stringify(invalidSaveResult.data, null, 2));

  // 测试无效的短 ID
  console.log('\n--- 测试无效的短 ID ---');
  const invalidIdResult = await makeRequest(`${BASE_URL}/api/conversations/invalid-id`);
  console.log('Invalid ID status:', invalidIdResult.status);
  console.log('Invalid ID response:', JSON.stringify(invalidIdResult.data, null, 2));

  // 测试不存在的会话
  console.log('\n--- 测试不存在的会话 ---');
  const notFoundResult = await makeRequest(`${BASE_URL}/api/conversations/9999999999`);
  console.log('Not found status:', notFoundResult.status);
  console.log('Not found response:', JSON.stringify(notFoundResult.data, null, 2));
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试会话 API...');
  console.log('Base URL:', BASE_URL);

  try {
    // 1. 测试保存会话
    const savedConversation = await testSaveConversation();
    if (!savedConversation) {
      console.log('❌ 保存会话失败，停止后续测试');
      return;
    }

    const shortId = savedConversation.short_id;
    console.log('📝 生成的短 ID:', shortId);

    // 2. 测试获取会话列表
    await testGetConversations();

    // 3. 测试获取单个会话
    await testGetConversation(shortId);

    // 4. 测试点赞会话
    await testLikeConversation(shortId);

    // 5. 测试更新会话
    await testUpdateConversation(shortId);

    // 6. 再次获取会话，验证更新和点赞是否生效
    console.log('\n=== 验证更新和点赞结果 ===');
    const finalConversation = await testGetConversation(shortId);
    if (finalConversation) {
      console.log('📊 最终统计:');
      console.log('- 浏览量:', finalConversation.view_count);
      console.log('- 点赞数:', finalConversation.like_count);
      console.log('- 标题:', finalConversation.title);
    }

    // 7. 测试无效参数
    await testInvalidRequests();

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
runTests();
