import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { ConversationService } from '~/lib/services/conversation-service';
import {
  validateConversationQueryParams,
  ApiErrorResponse,
} from '~/lib/types/conversations';

/**
 * 获取会话列表 API
 * GET /api/conversations
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    
    // 将 URLSearchParams 转换为普通对象
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });

    // 验证和清理查询参数
    const validatedParams = validateConversationQueryParams(params);

    // 创建会话服务实例
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 获取会话列表
    const result = await conversationService.getConversations(validatedParams);

    if (!result.success) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: result.error || 'Failed to fetch conversations',
        code: 'FETCH_FAILED',
      };
      return NextResponse.json(errorResponse, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Conversations list API error:', error);
    
    const errorResponse: ApiErrorResponse = {
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      code: 'INTERNAL_ERROR',
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
