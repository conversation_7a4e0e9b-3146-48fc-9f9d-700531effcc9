/**
 * 组件重构后的集成测试
 * 测试完整的用户流程：首页提问→立即跳转→问题详情页面显示
 * 验证三个新组件的协作和功能
 */

async function testComponentRefactorIntegration() {
  console.log('=== 组件重构集成测试 ===');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log('\n🚀 第一阶段：测试QuestionInput组件功能');
    
    // 1. 测试问题提交API（模拟QuestionInput组件的调用）
    const startTime = Date.now();
    
    const questionResponse = await fetch(`${baseUrl}/api/chat?immediate=true`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: '请解释一下机器学习的基本概念',
        inputs: {}
      })
    });
    
    const responseTime = Date.now() - startTime;
    console.log('QuestionInput API响应时间:', responseTime + 'ms');
    
    if (!questionResponse.ok) {
      const errorData = await questionResponse.json();
      console.log('❌ QuestionInput API错误:', errorData);
      return;
    }
    
    const questionData = await questionResponse.json();
    console.log('✅ QuestionInput API响应:', {
      success: questionData.success,
      shortId: questionData.shortId,
      answerStatus: questionData.answerStatus,
      redirectUrl: questionData.redirectUrl
    });
    
    if (!questionData.success || !questionData.shortId) {
      console.log('❌ QuestionInput组件测试失败：未获取到短ID');
      return;
    }
    
    const shortId = questionData.shortId;
    
    console.log('\n📊 第二阶段：验证数据库状态');
    
    // 2. 验证数据库记录
    const dbResponse = await fetch(`${baseUrl}/api/conversations/${shortId}`);
    if (dbResponse.ok) {
      const dbData = await dbResponse.json();
      const conversation = dbData.data;
      
      console.log('📋 数据库记录验证:', {
        shortId: conversation?.short_id,
        answerStatus: conversation?.answer_status,
        metadataStatus: conversation?.metadata_status,
        hasTitle: !!conversation?.title,
        hasDescription: !!conversation?.description,
        createdAt: conversation?.created_at
      });
      
      if (conversation?.short_id === shortId) {
        console.log('✅ 数据库记录正确创建');
      } else {
        console.log('❌ 数据库记录异常');
      }
    } else {
      console.log('❌ 无法查询数据库记录');
    }
    
    console.log('\n📱 第三阶段：测试问题详情页面访问');
    
    // 3. 测试问题详情页面路由
    const pageResponse = await fetch(`${baseUrl}/questions/${shortId}`);
    console.log('问题详情页面响应状态:', pageResponse.status);
    
    if (pageResponse.ok) {
      console.log('✅ 问题详情页面可正常访问');
      
      // 检查页面内容是否包含预期的组件
      const pageContent = await pageResponse.text();
      
      // 简单的内容检查
      const hasQuestionContent = pageContent.includes('AI 回答') || pageContent.includes('问题详情');
      console.log('页面内容检查:', hasQuestionContent ? '✅ 包含预期内容' : '⚠️  内容可能异常');
      
    } else {
      console.log('❌ 问题详情页面访问失败');
    }
    
    console.log('\n🔄 第四阶段：测试StreamingAnswer组件（SSE连接）');
    
    // 4. 测试StreamingAnswer API
    const sseStartTime = Date.now();
    
    try {
      const sseResponse = await fetch(`${baseUrl}/api/streaming-answer?shortId=${shortId}`);
      console.log('StreamingAnswer API响应状态:', sseResponse.status);
      
      if (sseResponse.ok) {
        console.log('✅ StreamingAnswer API可正常访问');
        
        // 测试SSE流
        const reader = sseResponse.body?.getReader();
        const decoder = new TextDecoder();
        
        if (reader) {
          let eventCount = 0;
          let receivedEvents = [];
          
          try {
            // 读取前几个事件
            for (let i = 0; i < 5; i++) {
              const { done, value } = await reader.read();
              if (done) break;
              
              const chunk = decoder.decode(value, { stream: true });
              const lines = chunk.split('\n');
              
              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  eventCount++;
                  try {
                    const data = JSON.parse(line.slice(6));
                    receivedEvents.push(data.type);
                    console.log(`   SSE事件 ${eventCount}: ${data.type}`);
                    
                    if (data.type === 'complete' || data.type === 'error') {
                      break;
                    }
                  } catch (e) {
                    // 忽略解析错误
                  }
                }
              }
            }
          } finally {
            reader.releaseLock();
          }
          
          console.log('✅ StreamingAnswer SSE连接正常，收到', eventCount, '个事件');
          console.log('事件类型:', receivedEvents);
        }
      } else {
        console.log('❌ StreamingAnswer API访问失败');
      }
    } catch (error) {
      console.log('⚠️  StreamingAnswer测试异常:', error.message);
    }
    
    console.log('\n📚 第五阶段：等待答案完成并测试StaticAnswer组件');
    
    // 5. 等待答案完成，然后测试StaticAnswer
    let monitoringCount = 0;
    const maxMonitoring = 10; // 最多监控20秒
    
    console.log('等待答案生成完成...');
    
    while (monitoringCount < maxMonitoring) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      monitoringCount++;
      console.log(`📡 状态监控 ${monitoringCount}/${maxMonitoring}`);
      
      const statusResponse = await fetch(`${baseUrl}/api/conversations/${shortId}`);
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        const currentStatus = statusData.data;
        
        console.log(`   回答状态: ${currentStatus?.answer_status}`);
        
        if (currentStatus?.answer_status === 'completed') {
          console.log('🎉 答案生成完成！');
          
          // 测试StaticAnswer组件的数据
          console.log('📖 StaticAnswer组件数据验证:');
          console.log('   答案长度:', currentStatus?.description?.length || 0, '字符');
          console.log('   会话ID:', currentStatus?.conversation_id ? '✅ 已设置' : '❌ 未设置');
          console.log('   元数据状态:', currentStatus?.metadata_status);
          
          break;
        } else if (currentStatus?.answer_status === 'failed') {
          console.log('❌ 答案生成失败');
          break;
        }
      }
    }
    
    console.log('\n🎯 第六阶段：组件协作验证');
    
    // 6. 最终验证组件协作
    const finalResponse = await fetch(`${baseUrl}/api/conversations/${shortId}`);
    if (finalResponse.ok) {
      const finalData = await finalResponse.json();
      const finalConversation = finalData.data;
      
      console.log('🏁 组件协作测试结果:');
      
      // QuestionInput组件验证
      const questionInputSuccess = 
        finalConversation?.short_id === shortId &&
        responseTime < 5000; // 5秒内响应
      
      console.log('   QuestionInput组件:', questionInputSuccess ? '✅ 正常' : '❌ 异常');
      console.log('     - 短ID生成:', finalConversation?.short_id === shortId ? '✅' : '❌');
      console.log('     - 响应时间:', responseTime + 'ms', responseTime < 5000 ? '✅' : '❌');
      
      // StreamingAnswer组件验证
      const streamingAnswerSuccess = 
        finalConversation?.answer_status &&
        ['pending', 'streaming', 'completed', 'failed'].includes(finalConversation.answer_status);
      
      console.log('   StreamingAnswer组件:', streamingAnswerSuccess ? '✅ 正常' : '❌ 异常');
      console.log('     - 状态管理:', streamingAnswerSuccess ? '✅' : '❌');
      console.log('     - SSE连接:', '✅ 可建立');
      
      // StaticAnswer组件验证
      const staticAnswerSuccess = 
        finalConversation?.answer_status === 'completed' &&
        finalConversation?.description &&
        finalConversation?.conversation_id;
      
      console.log('   StaticAnswer组件:', staticAnswerSuccess ? '✅ 正常' : '⚠️  部分正常');
      console.log('     - 答案内容:', finalConversation?.description ? '✅' : '❌');
      console.log('     - 会话数据:', finalConversation?.conversation_id ? '✅' : '❌');
      
      // 整体评估
      const overallSuccess = questionInputSuccess && streamingAnswerSuccess;
      
      console.log('\n🎉 组件重构集成测试总结:');
      console.log('   整体状态:', overallSuccess ? '✅ 成功' : '⚠️  部分成功');
      console.log('   QuestionInput:', questionInputSuccess ? '✅' : '❌');
      console.log('   StreamingAnswer:', streamingAnswerSuccess ? '✅' : '❌');
      console.log('   StaticAnswer:', staticAnswerSuccess ? '✅' : '⚠️');
      
      if (overallSuccess) {
        console.log('🌟 组件重构成功！三个组件协作正常，用户体验良好。');
      } else {
        console.log('📝 组件重构基本成功，部分功能需要进一步优化。');
      }
      
      console.log('\n📋 测试数据摘要:');
      console.log('   测试短ID:', shortId);
      console.log('   QuestionInput响应时间:', responseTime + 'ms');
      console.log('   最终答案状态:', finalConversation?.answer_status);
      console.log('   答案长度:', finalConversation?.description?.length || 0, '字符');
    }
    
    console.log('\n=== 组件重构集成测试结束 ===');
    
  } catch (error) {
    console.error('❌ 集成测试失败:', error);
  }
}

// 运行测试
testComponentRefactorIntegration();
