import { ChatClient } from 'dify-client';
import { DifyStreamChunk } from '../types';

/**
 * ChatflowService - 处理 Dify 聊天对话流程
 * 使用 ChatClient 处理聊天应用的流式响应
 */
export class ChatflowService {
  private client: ChatClient;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://api2.buyvs.com/v1') {
    this.baseUrl = baseUrl;
    this.client = new ChatClient(apiKey, baseUrl);
  }

  /**
   * 创建聊天消息并返回流式响应
   * @param query 用户查询
   * @param userId 用户ID
   * @param conversationId 可选的对话ID
   * @param inputs 可选的输入参数
   * @returns AsyncGenerator<DifyStreamChunk> 流式响应
   */
  async *chatStream(
    query: string,
    userId: string,
    conversationId?: string,
    inputs?: Record<string, any>
  ): AsyncGenerator<DifyStreamChunk> {
    try {
      console.log('ChatflowService.chatStream called with:', {
        query: query.substring(0, 50) + '...',
        userId,
        conversationId,
        inputs
      });

      const response = await this.client.createChatMessage(
        inputs || {},
        query,
        userId,
        true, // stream = true
        conversationId || null,
        null  // files = null
      );

      // 解析流式响应
      yield* this.parseStreamResponse(response);

    } catch (error) {
      console.error('ChatflowService.chatStream error:', error);
      throw error;
    }
  }

  /**
   * 创建非流式聊天消息
   * @param query 用户查询
   * @param userId 用户ID
   * @param conversationId 可选的对话ID
   * @param inputs 可选的输入参数
   * @returns Promise<any> 完整响应
   */
  async createChatMessage(
    query: string,
    userId: string,
    conversationId?: string,
    inputs?: Record<string, any>
  ): Promise<any> {
    try {
      console.log('ChatflowService.createChatMessage called with:', {
        query: query.substring(0, 50) + '...',
        userId,
        conversationId,
        inputs
      });

      const response = await this.client.createChatMessage(
        inputs || {},
        query,
        userId,
        false, // stream = false
        conversationId || null,
        null   // files = null
      );

      return response.data;

    } catch (error) {
      console.error('ChatflowService.createChatMessage error:', error);
      throw error;
    }
  }

  /**
   * 解析流式响应
   * @param response Axios 响应对象
   * @returns AsyncGenerator<DifyStreamChunk>
   */
  private async* parseStreamResponse(response: any): AsyncGenerator<DifyStreamChunk> {
    // 检查是否是 Axios 响应对象
    if (response && response.data && typeof response.data === 'object') {
      const stream = response.data;
      
      // 处理 Node.js 流
      if (stream && typeof stream.on === 'function') {
        let buffer = '';
        const chunks: DifyStreamChunk[] = [];
        let isComplete = false;

        // 监听数据事件
        stream.on('data', (chunk: Buffer) => {
          buffer += chunk.toString();
          
          // 按行分割数据
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留最后一个不完整的行
          
          for (const line of lines) {
            if (line.trim()) {
              try {
                // 解析 SSE 格式
                if (line.startsWith('data: ')) {
                  const data = line.slice(6).trim();
                  if (data && data !== '[DONE]') {
                    const parsed = JSON.parse(data);
                    chunks.push(parsed);
                  }
                }
              } catch (parseError) {
                console.warn('Failed to parse SSE line:', line, parseError);
              }
            }
          }
        });

        // 监听结束事件
        stream.on('end', () => {
          isComplete = true;
        });

        // 监听错误事件
        stream.on('error', (error: Error) => {
          console.error('Stream error:', error);
          isComplete = true;
        });

        // 等待并产出数据块
        while (!isComplete || chunks.length > 0) {
          if (chunks.length > 0) {
            const chunk = chunks.shift()!;
            console.log('Parsed chunk:', chunk);
            yield chunk;
          } else {
            // 等待更多数据
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }

        console.log('Stream ended');
      }
    }
  }

  /**
   * 更新 API 密钥
   * @param apiKey 新的 API 密钥
   */
  updateApiKey(apiKey: string): void {
    this.client.updateApiKey(apiKey);
  }
}
