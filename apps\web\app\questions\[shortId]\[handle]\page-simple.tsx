interface PageProps {
  params: Promise<{
    shortId: string;
    handle: string;
  }>;
}

export default async function QuestionDetailPage({ params }: PageProps) {
  const { shortId, handle } = await params;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-4xl px-4 py-8">
        <h1 className="text-2xl font-bold">Question Detail</h1>
        <p>Short ID: {shortId}</p>
        <p>Handle: {handle}</p>
      </div>
    </div>
  );
}
