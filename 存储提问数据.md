# 存储提问数据设计方案（简化版）

## 概述

基于 Dify API 直接获取会话数据的简化方案，避免复杂的数据同步，只存储必要的索引信息，通过编码实现美观的短链接。

## 核心设计理念

### 架构优势
- **数据源统一**：直接从 Dify API 获取实时数据，避免数据不一致
- **存储最小化**：只存储索引和元数据，减少存储成本
- **性能优化**：通过算法编码避免数据库查询
- **开发简化**：减少数据同步逻辑，降低系统复杂度

### 核心流程
```
用户问答 → Dify生成会话ID → 算法编码为短ID → 生成SEO友好URL → 社区展示
访问短链接 → 算法解码为会话ID → 调用Dify API → 获取问答内容
```

## 简化数据库设计

### 唯一表：conversations_index（会话索引表）

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | UUID | 主键，自动生成 | 550e8400-e29b-41d4-a716-************ |
| conversation_id | VARCHAR(255) | Dify会话ID | 1b7b58cd-53a6-4de0-96eb-64583ee7524b |
| short_id | VARCHAR(20) | 短链接ID（算法生成） | 4m2rzju |
| handle | VARCHAR(500) | URL友好标识符（AI生成） | solve-math-geometry-triangle-problem |
| title | VARCHAR(500) | 问题标题（AI生成） | 数学几何三角形求解问题 |
| description | VARCHAR(1000) | 问题描述（AI生成） | 解一个包含两个未知数的二元一次方程组的解法详细步骤 |
| language | VARCHAR(10) | 语言标识 | zh, en, ko |
| category_id | INT | 学科分类 ID（AI生成） | 1=数学, 2=物理, 3=化学 |
| user_id | UUID | 用户ID（注册用户） | 19cbf963-a787-44b0-a1ac-76c72a30d922 |
| is_public | BOOLEAN | 是否公开到社区 | true/false |
| user_type | TINYINT | 用户类型（0=游客,1=注册） | 0/1 |
| metadata_status | VARCHAR(20) | 元数据处理状态 | pending, processing, completed, failed |
| metadata_retry_count | INT | 元数据处理重试次数 | 0, 1, 2, 3 |
| answer_status | VARCHAR(20) | 回答状态 | pending, streaming, completed, failed |
| client_type | VARCHAR(20) | 客户端类型 | web, android, ios, desktop, extension, api |
| ai_model | VARCHAR(50) | AI模型标识 | gpt-4, claude-3, gemini-pro |
| conversation_user | TEXT | Dify对话用户标识符 | session_1704297600000_k3m9x7q2p |
| view_count | INT | 查看次数 | 0, 15, 100 |
| like_count | INT | 点赞次数 | 0, 5, 20 |
| created_at | TIMESTAMP | 创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | 更新时间 | 2024-01-01 10:30:00 |

**注意：** 问答内容直接从 Dify API 实时获取，不存储在本地数据库中。

### 分类 ID 映射表

| category_id | 中文名称 | 英文名称 | 说明 |
|-------------|----------|----------|------|
| 1 | 数学 | Mathematics | 代数、几何、微积分等 |
| 2 | 物理 | Physics | 力学、电学、光学等 |
| 3 | 化学 | Chemistry | 有机、无机、分析化学等 |
| 4 | 生物 | Biology | 细胞、遗传、生态等 |
| 5 | 历史 | History | 中国史、世界史等 |
| 6 | 语文 | Chinese | 文学、语法、写作等 |
| 7 | 英语 | English | 语法、阅读、写作等 |
| 8 | 地理 | Geography | 自然地理、人文地理等 |
| 9 | 政治 | Politics | 政治学、思想品德等 |
| 10 | 计算机 | Computer Science | 编程、算法、数据结构等 |
| 99 | 其他 | Others | 未分类或跨学科问题 |

### 用户类型说明

| user_type | 说明 | 权限 |
|-----------|------|------|
| 0 | 游客用户 | 可提问，默认私有，可选择公开 |
| 1 | 注册用户 | 可提问，可管理历史，可设置公开/私有 |

### 字段名称：metadata_status 数据类型：VARCHAR(20) 

说明：元数据处理状态 可能的值：

- pending - 等待处理
- processing - 处理中
- completed - 已完成
- failed - 处理失败

字段作用

用户提问后：状态设为 pending
启动元数据工作流后：状态更新为 processing
元数据生成完成后：状态更新为 completed
处理失败时：状态标记为 failed（支持后续重试）

### 字段名称：client_type 数据类型：VARCHAR(20)

说明：客户端类型标识，用于追踪用户使用的平台

可能的值：
- web - 网页版（默认）
- android - 安卓APP
- ios - 苹果APP
- desktop - 桌面应用
- extension - 浏览器插件
- api - API调用

字段作用：
- 用于数据分析和用户行为统计
- 支持不同平台的体验优化
- 为未来多平台扩展做准备

### 字段名称：ai_model 数据类型：VARCHAR(50)

说明：AI模型标识，记录使用的具体AI模型

可能的值：
- gpt-4 - OpenAI GPT-4
- gpt-3.5-turbo - OpenAI GPT-3.5 Turbo
- claude-3 - Anthropic Claude 3
- claude-3-sonnet - Anthropic Claude 3 Sonnet
- gemini-pro - Google Gemini Pro
- qwen-max - 阿里通义千问
- 其他支持的模型

字段作用：
- 追踪不同AI模型的使用情况
- 支持A/B测试和模型效果对比
- 成本分析和优化
- 模型升级效果评估

注意：该字段为必填字段，无默认值，需在写入时明确指定使用的模型。

### 字段名称：metadata_retry_count 数据类型：INT

说明：元数据处理重试次数，用于跟踪元数据处理的重试情况

可能的值：
- 0 - 初始状态，未进行重试
- 1 - 第一次重试
- 2 - 第二次重试
- 3 - 第三次重试（最大重试次数）

字段作用：
- 跟踪元数据处理的重试次数
- 支持失败任务的自动重试机制
- 防止无限重试，设置最大重试限制
- 便于监控和调试元数据处理的稳定性

重试逻辑：
- 初始值为 0（默认值）
- 元数据处理失败时，重试次数 +1
- 达到最大重试次数（3次）后，停止重试
- 成功处理后，保持当前重试次数不变

### 字段名称：answer_status 数据类型：VARCHAR(20)

说明：回答状态，用于区分流式模式和历史模式的页面显示

可能的值：
- pending - 问题已创建，等待回答
- streaming - 正在流式回答中
- completed - 回答完成
- failed - 回答失败

字段作用：
- 控制前端页面的显示模式（流式/历史）
- 支持超时检测和异常处理
- 提供用户友好的状态反馈
- 便于系统监控和调试

状态流转：
- 初始值为 'pending'（默认值）
- SSE连接建立成功后更新为 'streaming'
- 数据流正常结束后更新为 'completed'
- 连接超时或数据流中断时更新为 'failed'
- 用户手动重试时重置为 'pending'

显示逻辑：
- pending/streaming 状态：显示流式模式（SSE连接）
- completed 状态：显示历史模式（Dify API查询）
- failed 状态：显示错误模式（重试按钮）

注意事项：
- completed 状态是终态，历史查询失败不会改变此状态
- 只有用户手动重新提问才会创建新记录
- answer_status 与 metadata_status 独立管理，支持并行处理

## 业务逻辑设计

### 用户ID 处理策略

#### 注册用户
- 使用正常的用户ID（如：user_123）
- 可以管理自己的所有问答
- 支持个人中心查看历史记录

#### 游客用户
- 生成临时唯一标识：`guest_{timestamp}_{random}`
- 示例：`guest_1640995200_abc123`
- 通过浏览器本地存储记录游客身份
- 在社区中显示为"匿名用户"

### URL 生成策略

#### 短ID生成算法（当前实现）

**实际使用的策略**：随机字符串生成 + 数据库唯一性检查

1. **字符集**：使用去除混淆字符的字符集
   ```javascript
   // 去除容易混淆的字符：0, O, I, l, 1
   const CHARSET = '23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
   ```

2. **生成算法**：
   ```javascript
   // 生成 8 位随机短ID
   export function generateShortId(length: number = 8): string {
     let result = '';

     for (let i = 0; i < length; i++) {
       const randomIndex = Math.floor(Math.random() * CHARSET.length);
       result += CHARSET[randomIndex];
     }

     return result;
   }
   ```

3. **唯一性保证**：
   ```javascript
   // 带重试机制的唯一ID生成
   export async function generateUniqueShortId(
     checkUnique: (shortId: string) => Promise<boolean>,
     maxRetries: number = 10
   ): Promise<string> {
     for (let i = 0; i < maxRetries; i++) {
       const shortId = generateShortId();
       const isUnique = await checkUnique(shortId);

       if (isUnique) {
         return shortId;
       }
     }

     throw new Error('Failed to generate unique short ID after maximum retries');
   }
   ```

4. **实际使用示例**：
   - 生成的短ID：`3DxpQHZB`、`Q31HBLjs`、`LheaR1jy`
   - 长度：固定 8 位
   - 字符集：57 个字符（避免混淆）

#### SEO描述生成
1. 从问题内容提取关键词
2. 转换为 URL 友好格式（小写，连字符分隔）
3. 限制长度（建议50字符内）
4. 处理重复：添加数字后缀

#### 完整URL结构
```
https://yourdomain.com/questions/{short_id}/{slug}
示例：https://yourdomain.com/questions/3DxpQHZB/simple-addition-calculation
```

## 短链接ID存储方案

### 当前实现原理

**核心思路**：使用随机生成的短ID作为主键，通过数据库存储 `short_id` 到 `conversation_id` 的映射关系。

#### 数据流程
```
1. 用户提问 → Dify 生成 conversation_id
2. 随机生成短ID → 数据库唯一性检查
3. 存储映射关系到 conversations_index 表
4. 返回短链接给用户
```

#### 访问流程
```
1. 用户访问短链接 → 提取 short_id
2. 数据库查询 → 获取 conversation_id
3. 调用 Dify API → 获取对话内容
4. 渲染页面
```

#### 性能特点
```
URL访问 → 数据库查询 (indexed) → Dify API → 返回结果
性能：   ~5-10ms (数据库)     + API时间
优势：   实现简单，易于维护
```

#### 实际代码实现
```javascript
// 在 ConversationService 中的使用
async saveConversation(request: SaveConversationRequest) {
  // 生成唯一的短链接 ID
  const short_id = await generateUniqueShortId(async (id: string) => {
    const { data } = await this.supabase
      .from('conversations_index')
      .select('id')
      .eq('short_id', id)
      .maybeSingle();
    return !data; // 返回 true 表示唯一（不存在）
  });

  // 存储到数据库
  const insertData = {
    conversation_id,
    short_id,
    handle,
    title,
    description,
    // ... 其他字段
  };
}
```

### 数据获取流程

#### 会话创建时
1. 用户在 Dify 完成问答，获得会话ID
2. 随机生成短ID并检查唯一性
3. 生成SEO友好的URL handle
4. 保存映射关系到 conversations_index 表
5. 返回短链接给用户

#### 社区展示时
1. 从索引表获取公开会话列表
2. 通过 Dify API 批量获取问答摘要
3. 展示在社区页面

#### 详情页访问时
1. 从URL提取短ID：`/questions/3DxpQHZB/...`
2. 数据库查询获取会话ID：`52902aef-3776-41d8-8e35-ba08f4d8f51e`
3. 调用 Dify API 获取完整对话内容
4. 渲染问答详情页面

### 隐私和公开设置

#### 游客公开选择流程
1. 问答完成后显示选择框
2. 用户选择是否公开到社区
3. 默认设置：建议默认不公开，让用户主动选择
4. 后续修改：通过特殊链接允许修改设置

#### 隐私保护措施
- 游客在社区显示为"匿名用户"
- 不显示任何个人标识信息
- 提供举报和删除机制
- 敏感内容过滤

## 技术实现方案

### 重要修复记录

#### 前端显示问题修复（2025-08-03）

**问题描述**：
- 前端可以显示 `query`（用户问题），但 `answer`（AI回答）无法显示
- React Query 陷入无限重新请求循环
- 页面一直处于加载状态

**根本原因**：
1. **消息内容获取逻辑错误**：`getDifyMessageContent` 函数中的条件判断有误
2. **无限重新渲染**：`useEffect` 依赖项包含了会变化的 mutation 对象

**解决方案**：

1. **修复消息内容获取逻辑**：
```javascript
// 修复前（错误）
export function getDifyMessageContent(message: DifyMessage): string {
  if (message.query && message.answer) {
    return message.query; // 错误：应该返回 answer
  }
  // ...
}

// 修复后（正确）
export function getDifyMessageContent(message: DifyMessage): string {
  // 如果消息同时包含 query 和 answer，优先显示 answer（AI 回答）
  if (message.answer) {
    return message.answer;
  }

  if (message.query) {
    return message.query;
  }

  return '';
}
```

2. **修复无限重新渲染**：
```javascript
// 修复前（错误）
useEffect(() => {
  if (shortId) {
    incrementViewMutation.mutate(shortId);
  }
}, [shortId, incrementViewMutation]); // incrementViewMutation 导致无限循环

// 修复后（正确）
useEffect(() => {
  if (shortId) {
    incrementViewMutation.mutate(shortId);
  }
}, [shortId]); // 移除 incrementViewMutation 依赖
```

**修复结果**：
- ✅ 前端正确显示完整的 AI 回答内容
- ✅ 消除了无限重新请求问题
- ✅ 页面加载性能显著提升

### Dify API 集成

#### 获取会话详情
```javascript
// 根据会话ID获取完整对话内容
async function getConversationDetail(conversationId) {
  const response = await fetch(`${DIFY_API_URL}/conversations/${conversationId}/messages`, {
    headers: {
      'Authorization': `Bearer ${DIFY_API_KEY}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
}
```

#### 获取会话列表
```javascript
// 获取用户的会话列表（用于社区数据收集）
async function getUserConversations(userId, limit = 100) {
  const response = await fetch(`${DIFY_API_URL}/conversations`, {
    headers: {
      'Authorization': `Bearer ${DIFY_API_KEY}`
    },
    params: {
      user: userId,
      limit: limit
    }
  });

  return response.json();
}
```

### API 接口设计

#### 1. 聊天对话接口
```
POST /api/chat
Content-Type: application/json

请求体：
{
  "query": "1+2=?",
  "conversationId": "optional-conversation-id",
  "files": []
}

响应：
{
  "answer": "你好！1 + 2 的计算非常简单：\n\n1 + 2 = 3\n\n这是因为加法的意思是把两个数合起来，1和2合起来就是3。",
  "conversationId": "52902aef-3776-41d8-8e35-ba08f4d8f51e",
  "shortId": "3DxpQHZB",
  "url": "/questions/3DxpQHZB/simple-addition-calculation"
}
```

#### 2. 保存对话接口
```
POST /api/conversations/save
Content-Type: application/json

请求体：
{
  "conversation_id": "52902aef-3776-41d8-8e35-ba08f4d8f51e",
  "structured_output": {
    "title": "简单的加法计算",
    "description": "解释了1 + 2的加法计算过程和结果",
    "handle": "simple-addition-calculation",
    "language": "zh",
    "category": 1
  },
  "user_id": null,
  "user_type": 0,
  "is_public": false,
  "conversation_user": "user_1754180488155_jvh6rjw4i"
}

响应：
{
  "success": true,
  "shortId": "3DxpQHZB",
  "url": "/questions/3DxpQHZB/simple-addition-calculation"
}
```

#### 3. 获取对话详情接口
```
GET /api/conversations/{shortId}

响应：
{
  "id": "e33a56b0-da02-4591-b7e8-0ecc335e2494",
  "conversation_id": "52902aef-3776-41d8-8e35-ba08f4d8f51e",
  "short_id": "3DxpQHZB",
  "handle": "simple-addition-calculation",
  "title": "简单的加法计算",
  "description": "解释了1 + 2的加法计算过程和结果",
  "language": "zh",
  "category_id": 1,
  "user_id": null,
  "is_public": false,
  "user_type": 0,
  "conversation_user": "user_1754180488155_jvh6rjw4i",
  "view_count": 74,
  "like_count": 0,
  "created_at": "2025-08-03T00:21:32.82397+00:00",
  "updated_at": "2025-08-03T01:03:48.984357+00:00"
}
```

#### 4. 获取 Dify 对话消息接口
```
GET /api/dify/conversations/{conversationId}

响应：
{
  "conversation_id": "52902aef-3776-41d8-8e35-ba08f4d8f51e",
  "messages": [
    {
      "id": "2fe1c0c6-9d90-44cb-9791-aa3d4b68a45f",
      "conversation_id": "52902aef-3776-41d8-8e35-ba08f4d8f51e",
      "parent_message_id": "00000000-0000-0000-0000-000000000000",
      "inputs": {},
      "query": "1+2=?",
      "answer": "你好！1 + 2 的计算非常简单：\n\n1 + 2 = 3\n\n这是因为加法的意思是把两个数合起来，1和2合起来就是3。\n\n如果你有更多的数学问题，随时告诉我哦！",
      "message_files": [],
      "feedback": null,
      "retriever_resources": [],
      "created_at": 1754180488,
      "agent_thoughts": [],
      "status": "normal",
      "error": null
    }
  ]
}
```

#### 5. 社区问答列表接口
```
GET /api/community/questions
参数：
- page: 页码
- limit: 每页数量
- sort: 排序方式（latest/popular）

返回：索引表数据 + Dify API 实时获取的问答摘要
```

#### 6. 增加浏览量接口
```
POST /api/conversations/{shortId}/increment-view

响应：
{
  "success": true,
  "view_count": 75
}
```

#### 7. 点赞接口
```
POST /api/conversations/{shortId}/like

响应：
{
  "success": true,
  "like_count": 1
}
```

### 前端路由设计

#### 页面路由
- `/` - 首页（提问页面）
- `/questions` - 社区问答列表
- `/questions/{short_id}` - 重定向到完整URL
- `/questions/{short_id}/{slug}` - 问答详情页面
- `/ask` - 提问页面

#### 答案展示页面功能模块

**页面结构**：`/questions/{shortId}/{handle}`

**主要组件**：

1. **页面头部信息**
   - 问题标题（动态生成）
   - 问题描述
   - 创建时间和更新时间
   - 浏览量和点赞数统计

2. **问题元数据卡片**
   - 学科分类标签
   - 语言标识
   - 用户类型（注册用户/匿名用户）
   - 公开状态

3. **互动功能区**
   - 点赞按钮（带计数）
   - 分享按钮（复制链接）
   - 浏览量显示

4. **对话内容展示区**
   - 用户问题显示（带用户图标）
   - AI 回答显示（带 Bot 图标）
   - 支持 Markdown 格式渲染
   - 代码高亮显示
   - 数学公式渲染

5. **加载状态处理**
   - 骨架屏加载效果
   - 错误状态提示
   - 空内容提示

**技术实现**：

```typescript
// 页面组件结构
function ConversationDetailPage({
  conversation,
  initialData
}: {
  conversation?: ConversationData;
  initialData: ConversationData;
}) {
  // 使用 React Query 获取 Dify 对话数据
  const {
    data: difyConversation,
    isLoading: isDifyLoading,
    error: difyError
  } = useDifyConversation(conversationId || '');

  // 点赞功能
  const likeMutation = useLikeConversation();

  // 浏览量增加
  const incrementViewMutation = useIncrementViewCount();

  // 页面加载时增加浏览量
  useEffect(() => {
    if (shortId) {
      incrementViewMutation.mutate(shortId);
    }
  }, [shortId]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 问题信息卡片 */}
      <ConversationInfoCard conversation={currentConversation} />

      {/* 对话内容 */}
      <ConversationContent
        conversationId={conversationId || ''}
        difyConversation={difyConversation}
        isLoading={isDifyLoading}
        error={difyError}
      />
    </div>
  );
}
```

**数据流程**：

1. **服务端渲染（SSR）**：
   - 从数据库获取基本对话信息
   - 生成页面 meta 标签（SEO）
   - 预渲染页面结构

2. **客户端水合**：
   - 使用 React Query 获取 Dify 对话详情
   - 实时显示完整的问答内容
   - 处理用户交互（点赞、分享等）

3. **错误处理**：
   - Dify API 不可用时显示友好提示
   - 对话不存在时显示 404 页面
   - 网络错误时提供重试选项

#### SEO 优化
- 动态生成页面标题和描述
- 结构化数据标记（JSON-LD）
- 社交媒体分享优化（Open Graph）
- 服务端渲染支持搜索引擎抓取

### 性能优化

#### 数据库优化
- 为常用查询字段建立索引
- 分页查询优化
- 缓存热门问答

#### 前端优化
- 虚拟滚动处理大量数据
- 图片懒加载
- 页面缓存策略
- React Query 缓存配置：
  ```typescript
  // Dify 对话数据缓存配置
  const query = useQuery({
    queryKey: difyKeys.conversation(conversationId),
    queryFn: async () => {
      const response = await difyClient.getConversationDetail(conversationId);
      return response.data;
    },
    enabled: !!conversationId,
    staleTime: 2 * 60 * 1000, // 2 分钟内不重新请求
    gcTime: 5 * 60 * 1000, // 5 分钟后清理缓存
    retry: (failureCount, error) => {
      // 404 错误不重试
      if (error.message.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
  });
  ```

#### API 性能优化
- Dify API 响应缓存（Redis）
- 批量请求优化
- 请求去重和防抖
- 错误重试机制

## 实施计划

### ✅ 阶段1：编码算法实现（已完成）
1. ✅ 实现 UUID ↔ 短ID 双向转换算法
2. ✅ 创建简化的索引数据库表
3. ✅ 测试编码解码的准确性和性能

### ✅ 阶段2：Dify API 集成（已完成）
1. ✅ 实现会话数据获取接口
2. ✅ 开发问答内容解析逻辑
3. ✅ 建立API调用的缓存机制
4. ✅ 修复前端显示问题（消息内容获取逻辑）

### ✅ 阶段3：URL 路由系统（已完成）
1. ✅ 实现SEO友好的URL生成
2. ✅ 建立前端路由处理
3. ✅ 添加URL重定向逻辑

### ✅ 阶段4：基础功能开发（已完成）
1. ✅ 开发聊天对话接口
2. ✅ 实现问答详情页面（纯 Dify API）
3. ✅ 添加对话保存功能
4. ✅ 实现浏览量和点赞功能

### 🚧 阶段5：社区功能开发（进行中）
1. ⏳ 开发问答列表页面（索引表 + Dify API）
2. ⏳ 添加用户公开设置功能
3. ⏳ 实现搜索和筛选功能

### 📋 阶段6：性能优化（待开始）
1. ⏳ 实现 Dify API 响应缓存（Redis）
2. ⏳ 优化页面加载速度
3. ⏳ 添加CDN和静态资源优化
4. ⏳ 实现服务端渲染优化

## 方案优势总结

### 技术优势
- **实现简单**：随机生成 + 数据库存储，逻辑清晰易维护
- **高可靠性**：数据库索引保证查询性能，避免算法复杂性
- **数据一致性**：直接从 Dify API 获取，避免同步延迟和不一致
- **存储成本低**：只存储必要的索引信息，大幅减少存储需求
- **扩展性好**：支持更多元数据存储，便于功能扩展

### 用户体验优势
- **URL美观**：短链接易于分享和记忆（如：`3DxpQHZB`）
- **SEO友好**：支持自定义slug和完整URL结构
- **实时数据**：问答内容始终是最新的
- **稳定访问**：数据库索引保证快速查询

## 注意事项

### 技术风险
- **API依赖**：依赖 Dify API 的稳定性和响应速度
- **短ID冲突**：随机生成可能产生冲突，已通过重试机制解决
- **数据库依赖**：需要数据库查询获取映射关系
- **缓存策略**：合理设置 API 响应缓存，平衡性能和实时性

### 已解决的技术问题
- ✅ **前端显示问题**：修复了消息内容获取逻辑错误
- ✅ **无限重新渲染**：移除了导致循环的依赖项
- ✅ **React Query 配置**：优化了缓存和重试策略
- ✅ **错误处理**：完善了 404 和网络错误的处理
- ✅ **短ID唯一性**：实现了带重试机制的唯一ID生成
- ✅ **数据库索引**：为 short_id 字段建立了唯一索引

### 扩展性考虑
- **API限流**：考虑 Dify API 的调用频率限制
- **缓存优化**：实现多层缓存策略（Redis、CDN等）
- **降级方案**：API不可用时的备用方案

### 数据安全
- **敏感信息过滤**：从 API 响应中过滤敏感内容
- **访问控制**：确保只能访问公开的会话
- **内容审核**：对公开内容进行适当的审核机制

### 监控和调试
- **日志记录**：完善的 API 调用和错误日志
- **性能监控**：React Query DevTools 支持
- **用户体验监控**：页面加载时间和错误率统计

### 当前系统状态
- **基础功能**：✅ 完全可用
- **问答展示**：✅ 正常显示用户问题和 AI 回答
- **URL 路由**：✅ SEO 友好的短链接系统
- **数据持久化**：✅ 对话索引和元数据存储
- **用户交互**：✅ 点赞、分享、浏览量统计

## 当前实现的 API 端点总结

### 已实现的 API 路由
```
POST   /api/chat                           # 聊天对话
POST   /api/conversations/save             # 保存对话
GET    /api/conversations/[id]             # 获取对话详情
POST   /api/conversations/[id]/like        # 点赞对话
GET    /api/dify/conversations/[id]        # 获取 Dify 对话消息
POST   /api/upload                         # 文件上传
```

### 文件结构
```
apps/web/app/api/
├── chat/
│   └── route.ts                    # 聊天对话接口
├── conversations/
│   ├── [id]/
│   │   ├── route.ts               # 获取对话详情
│   │   └── like/
│   │       └── route.ts           # 点赞功能
│   ├── route.ts                   # 对话列表（待实现）
│   └── save/
│       └── route.ts               # 保存对话
├── dify/
│   └── conversations/
│       └── [id]/
│           └── route.ts           # 获取 Dify 对话消息
└── upload/
    └── route.ts                   # 文件上传
```

### 前端页面路由
```
/                                   # 首页（聊天界面）
/questions/[shortId]/[handle]       # 问答详情页面
```

### 核心技术栈
- **后端框架**：Next.js 15 App Router
- **数据库**：Supabase (PostgreSQL)
- **状态管理**：React Query (TanStack Query)
- **UI 组件**：Radix UI + Tailwind CSS
- **AI 服务**：Dify API
- **部署**：Vercel (推荐)

### 数据流程图
```
用户提问 → /api/chat → Dify API → 生成回答
    ↓
随机生成短ID → 数据库唯一性检查 → 保存映射关系
    ↓
生成短链接 → /questions/{shortId}/{handle}
    ↓
访问详情页 → 数据库查询 conversation_id → /api/dify/conversations/{id} → 显示完整对话
```

### 短ID工具函数总结

**文件位置**：`apps/web/lib/utils/short-id.ts`

**核心函数**：
- `generateShortId(length = 8)` - 生成随机短ID
- `generateUniqueShortId(checkUnique, maxRetries = 10)` - 生成唯一短ID
- `isValidShortId(shortId)` - 验证短ID格式
- `generateQuestionUrl(shortId, handle)` - 生成完整URL
- `extractShortIdFromUrl(url)` - 从URL提取短ID

**字符集特点**：
- 57个字符（去除混淆字符：0, O, I, l, 1）
- 固定8位长度
- 支持大小写字母和数字