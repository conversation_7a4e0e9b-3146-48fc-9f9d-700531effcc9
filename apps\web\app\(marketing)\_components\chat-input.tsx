'use client';

import { useState, useCallback } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Textarea } from '@kit/ui/textarea';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { FileUpload } from './file-upload';
import { ErrorDisplay, createErrorInfo, RetryButton } from './error-handling';
import { LoadingSpinner } from './loading-states';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: 'image' | 'document';
  mimeType: string;
  url?: string;
}

interface ChatMessage {
  id: string;
  answer: string;
  conversationId: string;
  shortId: string;
  createdAt: number;
}

interface ChatInputProps {
  onSendMessage: (message: string, files: UploadedFile[]) => Promise<ChatMessage>;
  onMessageSent?: (message: ChatMessage) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function ChatInput({
  onSendMessage,
  onMessageSent,
  placeholder = "Type or paste your question here, or use '/' for formatting. For the best answer, ask one question at a time.",
  disabled = false
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<any>(null);
  const [retryCount, setRetryCount] = useState(0);

  const handleSend = useCallback(async () => {
    if (!message.trim() && files.length === 0) {
      setError(createErrorInfo('Please enter a question or upload a file', 'validation'));
      return;
    }

    setSending(true);
    setError(null);

    try {
      const response = await onSendMessage(message.trim(), files);

      // 清空输入和重置状态
      setMessage('');
      setFiles([]);
      setRetryCount(0);

      // 通知父组件
      onMessageSent?.(response);

    } catch (err) {
      console.error('Send message error:', err);
      const errorInfo = createErrorInfo(err, 'chat');
      setError(errorInfo);
      setRetryCount(prev => prev + 1);
    } finally {
      setSending(false);
    }
  }, [message, files, onSendMessage, onMessageSent]);

  const handleRetry = useCallback(() => {
    handleSend();
  }, [handleSend]);

  const handleDismissError = useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  const canSend = (message.trim().length > 0 || files.length > 0) && !sending && !disabled;

  return (
    <div className="space-y-4">
      {/* File Upload */}
      <FileUpload 
        onFilesChange={setFiles}
        maxFiles={3}
      />

      {/* Text Input */}
      <div className="space-y-3">
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="min-h-[120px] resize-none"
          disabled={sending || disabled}
        />

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              <Trans i18nKey="chat:community" defaults="Community" />
            </Badge>
            <Badge variant="secondary" className="bg-gradient-to-r from-primary to-secondary text-primary-foreground">
              ⭐ <Trans i18nKey="chat:superAI" defaults="Super AI" />
            </Badge>
          </div>

          <Button 
            onClick={handleSend}
            disabled={!canSend}
            className="px-8"
          >
            {sending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                <Trans i18nKey="chat:sending" defaults="Sending..." />
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                <Trans i18nKey="chat:getAnswer" defaults="Get answer" />
              </>
            )}
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <ErrorDisplay
            error={error}
            onRetry={error.retryable ? handleRetry : undefined}
            onDismiss={handleDismissError}
            showDetails={false}
          />
        )}
      </div>
    </div>
  );
}
