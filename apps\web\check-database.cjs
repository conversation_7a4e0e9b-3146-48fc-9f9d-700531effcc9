/**
 * 检查数据库表是否存在并创建
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少 Supabase 配置');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAndCreateTable() {
  console.log('🔍 检查 conversations_index 表...');
  
  try {
    // 尝试查询表
    const { data, error } = await supabase
      .from('conversations_index')
      .select('count')
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') {
        console.log('❌ 表不存在，正在创建...');
        await createTable();
      } else {
        console.error('❌ 数据库错误:', error.message);
        return false;
      }
    } else {
      console.log('✅ 表已存在');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    return false;
  }
}

async function createTable() {
  console.log('🔨 创建 conversations_index 表...');
  
  // 读取迁移文件
  const fs = require('fs');
  const path = require('path');
  
  try {
    const migrationPath = path.join(__dirname, 'supabase', 'migrations', '20241220000000_conversations_index.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 执行迁移文件...');
    
    // 分割 SQL 语句并逐个执行
    const statements = migrationSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log('执行:', statement.substring(0, 50) + '...');
        
        const { error } = await supabase.rpc('exec_sql', { 
          sql: statement + ';' 
        });
        
        if (error) {
          console.error('❌ SQL 执行失败:', error.message);
          console.error('SQL:', statement);
        } else {
          console.log('✅ SQL 执行成功');
        }
      }
    }
    
    console.log('✅ 表创建完成');
    return true;
  } catch (error) {
    console.error('❌ 创建表失败:', error.message);
    return false;
  }
}

async function testInsert() {
  console.log('🧪 测试插入数据...');
  
  const testData = {
    conversation_id: '550e8400-e29b-41d4-a716-446655440000',
    short_id: 'test123',
    handle: 'test-handle',
    title: '测试标题',
    description: '测试描述',
    language: 'zh',
    category_id: 99,
    user_id: null,
    is_public: true,
    user_type: 0,
    view_count: 0,
    like_count: 0,
  };
  
  try {
    const { data, error } = await supabase
      .from('conversations_index')
      .insert(testData)
      .select()
      .single();
    
    if (error) {
      console.error('❌ 插入失败:', error.message);
      return false;
    }
    
    console.log('✅ 插入成功:', data.id);
    
    // 清理测试数据
    await supabase
      .from('conversations_index')
      .delete()
      .eq('id', data.id);
    
    console.log('✅ 清理完成');
    return true;
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 开始数据库检查...\n');
  
  const tableReady = await checkAndCreateTable();
  if (!tableReady) {
    console.log('❌ 数据库准备失败');
    return;
  }
  
  const testPassed = await testInsert();
  if (testPassed) {
    console.log('\n🎉 数据库检查完成，一切正常！');
  } else {
    console.log('\n❌ 数据库测试失败');
  }
}

main().catch(console.error);
