async function testIntegration() {
  console.log('=== 集成测试开始 ===');
  
  try {
    // 第一步：测试聊天 API
    console.log('\n1. 测试聊天 API...');
    const chatResponse = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'What is the square root of 16?',
        inputs: {}
      })
    });

    console.log('聊天响应状态:', chatResponse.status);

    if (!chatResponse.ok) {
      const errorText = await chatResponse.text();
      console.log('聊天错误:', errorText);
      return;
    }

    // 解析流式响应
    const reader = chatResponse.body.getReader();
    const decoder = new TextDecoder();
    let fullAnswer = '';
    let conversationId = null;
    let shortId = null;
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('聊天流结束');
          break;
        }
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.type === 'answer') {
                fullAnswer += data.content;
              } else if (data.type === 'done') {
                conversationId = data.conversation_id;
                shortId = data.short_id;
                console.log('获得对话ID:', conversationId);
                console.log('获得短ID:', shortId);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    console.log('完整答案长度:', fullAnswer.length);
    console.log('答案预览:', fullAnswer.substring(0, 100) + '...');

    if (!conversationId || !shortId) {
      console.error('未获得对话ID或短ID');
      return;
    }

    // 第二步：等待一下，然后测试元数据处理
    console.log('\n2. 等待2秒后测试元数据处理...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    const metadataResponse = await fetch('http://localhost:3000/api/chat/metadata', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        conversation_id: conversationId, // Dify 对话ID
        short_id: shortId,               // 系统短ID
        query: 'What is the square root of 16?',
        answer: fullAnswer
      })
    });

    console.log('元数据响应状态:', metadataResponse.status);
    
    if (metadataResponse.ok) {
      const metadataData = await metadataResponse.json();
      console.log('元数据处理成功!');
      console.log('标题:', metadataData.data?.structured_output?.title);
      console.log('描述:', metadataData.data?.structured_output?.description);
      console.log('处理状态:', metadataData.data?.metadata_status);
    } else {
      const errorText = await metadataResponse.text();
      console.log('元数据处理错误:', errorText);
    }

    // 第三步：测试元数据状态查询
    console.log('\n3. 测试元数据状态查询...');
    const statusResponse = await fetch(`http://localhost:3000/api/chat/metadata?conversation_id=${shortId}`);
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      console.log('状态查询成功!');
      console.log('当前状态:', statusData.data?.metadata_status);
      console.log('标题:', statusData.data?.metadata?.title);
      console.log('描述:', statusData.data?.metadata?.description);
    } else {
      const errorText = await statusResponse.text();
      console.log('状态查询错误:', errorText);
    }

    console.log('\n=== 集成测试完成 ===');

  } catch (error) {
    console.error('集成测试失败:', error);
  }
}

testIntegration();
