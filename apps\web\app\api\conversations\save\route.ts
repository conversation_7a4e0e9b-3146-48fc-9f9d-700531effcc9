import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { ConversationService } from '~/lib/services/conversation-service';
import {
  validateSaveConversationRequest,
  SaveConversationRequest,
  ApiErrorResponse,
  ClientType,
} from '~/lib/types/conversations';

/**
 * 保存对话索引 API
 * POST /api/conversations/save
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();

    // 验证请求参数
    if (!validateSaveConversationRequest(body)) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Invalid request parameters',
        details: 'Missing required fields: conversation_id, structured_output',
        code: 'INVALID_PARAMS',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    console.log('=== Save API Debug ===');
    console.log('Received body:', JSON.stringify(body, null, 2));
    console.log('body.conversation_user:', body.conversation_user);

    const requestData: SaveConversationRequest = {
      conversation_id: body.conversation_id,
      structured_output: body.structured_output,
      user_id: body.user_id || null,
      user_type: body.user_type || 0,
      is_public: body.is_public || false,
      conversation_user: body.conversation_user, // 添加 conversation_user 字段
      client_type: body.client_type || ClientType.WEB, // 添加 client_type 字段
      ai_model: body.ai_model || 'gpt-4.1-mini', // 添加 ai_model 字段，设置默认值
    };

    console.log('Processed requestData:', JSON.stringify(requestData, null, 2));
    console.log('======================');

    // 创建会话服务实例（使用 Admin Client 绕过 RLS 进行保存操作）
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 保存会话
    const result = await conversationService.saveConversation(requestData);

    if (!result.success) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: result.error || 'Failed to save conversation',
        details: result.details,
        code: 'SAVE_FAILED',
      };
      return NextResponse.json(errorResponse, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Conversation save API error:', error);
    
    const errorResponse: ApiErrorResponse = {
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      code: 'INTERNAL_ERROR',
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
