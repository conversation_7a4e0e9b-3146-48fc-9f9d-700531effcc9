'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Library,
  FileText,
  Video,
  BookOpen,
  MoreHorizontal,
  Moon,
  Globe,
  User,
  Zap
} from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@kit/ui/shadcn-sidebar';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

const navigationItems = [
  {
    title: 'Home',
    href: '/',
    icon: Home,
  },
  {
    title: 'Library',
    href: '/library',
    icon: Library,
  },
];

const studyTools = [
  {
    title: 'Homework Help',
    href: '/homework-help',
    icon: FileText,
  },
  {
    title: 'AI Note Taker',
    href: '/ai-note-taker',
    icon: BookOpen,
  },
  {
    title: 'PDF Summarizer',
    href: '/pdf-summarizer',
    icon: FileText,
  },
  {
    title: 'Video Summarizer',
    href: '/video-summarizer',
    icon: Video,
  },
  {
    title: 'AI Lecture Note',
    href: '/ai-lecture-note',
    icon: BookOpen,
  },
  {
    title: 'More Tools',
    href: '/more-tools',
    icon: MoreHorizontal,
  },
];

export function SidebarNavigation() {
  const pathname = usePathname();

  return (
    <Sidebar className="w-64">
      <SidebarHeader className="p-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                fill="white"
              />
            </svg>
          </div>
          <span className="text-xl font-bold">StudyX</span>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton asChild isActive={pathname === item.href}>
                    <Link href={item.href}>
                      <item.icon className="w-5 h-5" />
                      <span><Trans i18nKey={`navigation:${item.title.toLowerCase()}`} defaults={item.title} /></span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Study Tools */}
        <SidebarGroup>
          <SidebarGroupLabel>
            <Trans i18nKey="navigation:studyTools" defaults="Study Tools" />
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {studyTools.map((tool) => (
                <SidebarMenuItem key={tool.href}>
                  <SidebarMenuButton asChild isActive={pathname === tool.href}>
                    <Link href={tool.href}>
                      <tool.icon className="w-4 h-4" />
                      <span><Trans i18nKey={`navigation:${tool.title.toLowerCase().replace(/\s+/g, '')}`} defaults={tool.title} /></span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Explore & Help */}
        <SidebarGroup>
          <SidebarGroupLabel>
            <Trans i18nKey="navigation:exploreHelp" defaults="Explore & Help" />
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link href="/resources">
                    <Library className="w-4 h-4" />
                    <span><Trans i18nKey="navigation:resource" defaults="Resource" /></span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-4 space-y-3">
        {/* Settings */}
        <div className="space-y-2">
          <Button variant="ghost" size="sm" className="w-full justify-start">
            <Moon className="w-4 h-4 mr-2" />
            <Trans i18nKey="navigation:darkMode" defaults="Dark Mode" />
          </Button>

          <Button variant="ghost" size="sm" className="w-full justify-start">
            <Globe className="w-4 h-4 mr-2" />
            <Trans i18nKey="navigation:language" defaults="English" />
          </Button>
        </div>

        {/* User Section */}
        <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-primary-foreground" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">name user</p>
            <p className="text-xs text-muted-foreground">Basic</p>
          </div>
        </div>

        {/* Upgrade Button */}
        <Button className="w-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600">
          <Zap className="w-4 h-4 mr-2" />
          <Trans i18nKey="navigation:upgrade" defaults="Upgrade" />
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}
