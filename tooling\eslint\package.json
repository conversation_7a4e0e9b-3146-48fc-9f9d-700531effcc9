{"name": "@kit/eslint-config", "version": "0.2.0", "private": true, "type": "module", "files": ["./apps.js", "./base.js", "./nextjs.js"], "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{js,json}\""}, "dependencies": {"@next/eslint-plugin-next": "15.4.3", "@types/eslint": "9.6.1", "eslint-config-next": "15.4.3", "eslint-config-turbo": "^2.5.5", "typescript-eslint": "8.38.0"}, "devDependencies": {"@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "eslint": "^9.31.0", "typescript": "^5.8.3"}, "prettier": "@kit/prettier-config"}