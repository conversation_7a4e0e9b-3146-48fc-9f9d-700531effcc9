/**
 * React Query hooks for conversation management
 * 提供会话数据的缓存、加载状态和错误处理
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@kit/ui/sonner';

import {
  conversationClient,
  isApiSuccess,
  isApiError,
} from '../services/conversation-client';
import {
  ConversationIndexRow,
  SaveConversationRequest,
  ConversationQueryParams,
  UpdateConversationRequest,
} from '../types/conversations';

/**
 * Query Keys for React Query
 */
export const conversationKeys = {
  all: ['conversations'] as const,
  lists: () => [...conversationKeys.all, 'list'] as const,
  list: (params: Partial<ConversationQueryParams>) =>
    [...conversationKeys.lists(), params] as const,
  details: () => [...conversationKeys.all, 'detail'] as const,
  detail: (shortId: string) => [...conversationKeys.details(), shortId] as const,
};

/**
 * Hook for fetching conversation list
 */
export function useConversations(params: Partial<ConversationQueryParams> = {}) {
  return useQuery({
    queryKey: conversationKeys.list(params),
    queryFn: async () => {
      const response = await conversationClient.getConversations(params);
      
      if (isApiError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for fetching single conversation
 */
export function useConversation(shortId: string) {
  return useQuery({
    queryKey: conversationKeys.detail(shortId),
    queryFn: async () => {
      const response = await conversationClient.getConversation(shortId);
      
      if (isApiError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    enabled: !!shortId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for saving conversation
 */
export function useSaveConversation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SaveConversationRequest) => {
      const response = await conversationClient.saveConversation(data);
      
      if (isApiError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate conversation lists to refresh data
      queryClient.invalidateQueries({ queryKey: conversationKeys.lists() });
      
      // Add the new conversation to the cache
      queryClient.setQueryData(
        conversationKeys.detail(data.short_id),
        data
      );
      
      toast.success('会话保存成功');
    },
    onError: (error: Error) => {
      toast.error(`保存失败: ${error.message}`);
    },
  });
}

/**
 * Hook for updating conversation
 */
export function useUpdateConversation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      shortId,
      data,
    }: {
      shortId: string;
      data: UpdateConversationRequest;
    }) => {
      const response = await conversationClient.updateConversation(shortId, data);
      
      if (isApiError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Update the conversation in cache
      queryClient.setQueryData(
        conversationKeys.detail(variables.shortId),
        data
      );
      
      // Invalidate lists to refresh data
      queryClient.invalidateQueries({ queryKey: conversationKeys.lists() });
      
      toast.success('会话更新成功');
    },
    onError: (error: Error) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });
}

/**
 * Hook for liking conversation
 */
export function useLikeConversation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (shortId: string) => {
      const response = await conversationClient.likeConversation(shortId);
      
      if (isApiError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    onSuccess: (data, shortId) => {
      // Update the conversation in cache
      queryClient.setQueryData(
        conversationKeys.detail(shortId),
        data
      );
      
      // Optimistically update lists
      queryClient.invalidateQueries({ queryKey: conversationKeys.lists() });
      
      toast.success('点赞成功');
    },
    onError: (error: Error) => {
      toast.error(`点赞失败: ${error.message}`);
    },
  });
}

/**
 * Hook for deleting conversation
 */
export function useDeleteConversation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (shortId: string) => {
      const response = await conversationClient.deleteConversation(shortId);
      
      if (isApiError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    onSuccess: (_, shortId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: conversationKeys.detail(shortId) });
      
      // Invalidate lists to refresh data
      queryClient.invalidateQueries({ queryKey: conversationKeys.lists() });
      
      toast.success('会话删除成功');
    },
    onError: (error: Error) => {
      toast.error(`删除失败: ${error.message}`);
    },
  });
}

/**
 * Hook for incrementing view count (optimistic update)
 */
export function useIncrementViewCount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (shortId: string) => {
      // This is an optimistic update - we don't wait for the server response
      // The actual increment happens on the server when the page is viewed
      return shortId;
    },
    onMutate: async (shortId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: conversationKeys.detail(shortId) });

      // Snapshot the previous value
      const previousConversation = queryClient.getQueryData<ConversationIndexRow>(
        conversationKeys.detail(shortId)
      );

      // Optimistically update to the new value
      if (previousConversation) {
        queryClient.setQueryData<ConversationIndexRow>(
          conversationKeys.detail(shortId),
          {
            ...previousConversation,
            view_count: previousConversation.view_count + 1,
          }
        );
      }

      // Return a context object with the snapshotted value
      return { previousConversation };
    },
    onError: (err, shortId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousConversation) {
        queryClient.setQueryData(
          conversationKeys.detail(shortId),
          context.previousConversation
        );
      }
    },
  });
}
