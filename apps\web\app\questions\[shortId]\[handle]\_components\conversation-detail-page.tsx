'use client';

import { useEffect, useState } from 'react';
import { ArrowLeft, Eye, Heart, Share2, Calendar, User, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Card, CardContent, CardHeader } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';
import { Alert, AlertDescription } from '@kit/ui/alert';

import { useConversation, useLikeConversation, useIncrementViewCount } from '~/lib/hooks/use-conversations';
import { useDifyConversation } from '~/lib/hooks/use-dify';
import { ConversationIndexRow, MetadataStatus } from '~/lib/types/conversations';
import {
  getCategoryInfo,
  formatTimeAgo,
  formatNumber,
  getUserTypeText,
  getShareUrl,
} from '~/lib/utils/conversation-utils';

import { ConversationContent } from './conversation-content';
import { ConversationDetailSkeleton } from './conversation-detail-skeleton';

interface ConversationDetailPageProps {
  shortId: string;
  initialData?: ConversationIndexRow;
}

export function ConversationDetailPage({
  shortId,
  initialData
}: ConversationDetailPageProps) {
  const router = useRouter();
  const [metadataStatus, setMetadataStatus] = useState<MetadataStatus | null>(null);
  const [isMonitoringMetadata, setIsMonitoringMetadata] = useState(false);

  // 获取会话基本信息
  const {
    data: conversation,
    isLoading: isConversationLoading,
    error: conversationError
  } = useConversation(shortId);

  // 获取 Dify 对话内容
  const conversationId = conversation?.conversation_id || initialData?.conversation_id;

  console.log('ConversationDetailPage Debug:', {
    conversation,
    initialData,
    conversationId,
    shortId
  });

  const {
    data: difyConversation,
    isLoading: isDifyLoading,
    error: difyError
  } = useDifyConversation(conversationId || '');

  // 点赞功能
  const likeMutation = useLikeConversation();
  
  // 浏览量增加
  const incrementViewMutation = useIncrementViewCount();

  // 使用初始数据或查询数据
  const currentConversation = conversation || initialData;

  // 页面加载时增加浏览量
  useEffect(() => {
    if (shortId) {
      incrementViewMutation.mutate(shortId);
    }
  }, [shortId]); // 移除 incrementViewMutation 依赖，避免无限重新渲染

  // 元数据状态监控
  useEffect(() => {
    const currentConversation = conversation || initialData;
    if (!currentConversation) return;

    setMetadataStatus(currentConversation.metadata_status as MetadataStatus);

    // 如果元数据状态是 processing，启动轮询监控
    if (currentConversation.metadata_status === MetadataStatus.PROCESSING) {
      setIsMonitoringMetadata(true);

      const pollMetadataStatus = async () => {
        try {
          const response = await fetch(`/api/chat/metadata?conversation_id=${shortId}`);
          if (response.ok) {
            const data = await response.json();
            const newStatus = data.data?.metadata_status;

            setMetadataStatus(newStatus);

            // 如果状态变为完成或失败，停止监控
            if (newStatus === MetadataStatus.COMPLETED || newStatus === MetadataStatus.FAILED) {
              setIsMonitoringMetadata(false);
              // 刷新会话数据以获取最新的元数据
              window.location.reload();
            }
          }
        } catch (error) {
          console.error('Failed to poll metadata status:', error);
        }
      };

      // 每5秒轮询一次
      const interval = setInterval(pollMetadataStatus, 5000);

      // 清理函数
      return () => {
        clearInterval(interval);
        setIsMonitoringMetadata(false);
      };
    }
  }, [conversation, initialData, shortId]);

  // 处理点赞
  const handleLike = () => {
    if (shortId) {
      likeMutation.mutate(shortId);
    }
  };

  // 处理分享
  const handleShare = async () => {
    if (!currentConversation) return;

    const shareUrl = getShareUrl(currentConversation);
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: currentConversation.title,
          text: currentConversation.description,
          url: shareUrl,
        });
      } catch (error) {
        // 用户取消分享或分享失败，复制到剪贴板
        await navigator.clipboard.writeText(shareUrl);
      }
    } else {
      // 不支持 Web Share API，复制到剪贴板
      await navigator.clipboard.writeText(shareUrl);
    }
  };

  // 错误处理
  if (conversationError) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">问题不存在</h1>
        <p className="text-gray-600 mb-6">您访问的问题不存在或已被删除。</p>
        <Button asChild>
          <Link href="/">返回首页</Link>
        </Button>
      </div>
    );
  }

  // 加载状态
  if (isConversationLoading && !initialData) {
    return <ConversationDetailSkeleton />;
  }

  if (!currentConversation) {
    return null;
  }

  const category = getCategoryInfo(currentConversation.category_id);

  return (
    <div className="space-y-6">
      {/* 返回按钮 */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>
      </div>

      {/* 问题标题和元信息 */}
      <Card>
        <CardHeader className="space-y-4">
          <div className="flex flex-wrap items-center gap-2">
            <Badge
              variant="secondary"
              className={category.color}
            >
              {category.icon} {category.name}
            </Badge>
            <Badge variant="outline">
              {getUserTypeText(currentConversation.user_type)}
            </Badge>
            {currentConversation.is_public && (
              <Badge variant="outline" className="bg-green-50 text-green-700">
                公开
              </Badge>
            )}

            {/* 元数据状态指示器 */}
            <MetadataStatusBadge
              status={metadataStatus}
              isMonitoring={isMonitoringMetadata}
            />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 leading-tight">
            {currentConversation.title}
          </h1>

          <p className="text-gray-600 leading-relaxed">
            {currentConversation.description}
          </p>

          {/* 统计信息 */}
          <div className="flex items-center gap-6 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{formatNumber(currentConversation.view_count)} 浏览</span>
            </div>
            <div className="flex items-center gap-1">
              <Heart className="h-4 w-4" />
              <span>{formatNumber(currentConversation.like_count)} 点赞</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>{formatTimeAgo(currentConversation.created_at)}</span>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* 操作按钮 */}
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleLike}
              disabled={likeMutation.isPending}
              className="flex items-center gap-2"
            >
              <Heart className="h-4 w-4" />
              点赞 ({currentConversation.like_count})
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="flex items-center gap-2"
            >
              <Share2 className="h-4 w-4" />
              分享
            </Button>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* 对话内容 */}
      <ConversationContent
        conversationId={conversationId || ''}
        difyConversation={difyConversation}
        isLoading={isDifyLoading}
        error={difyError}
      />
    </div>
  );
}

/**
 * 元数据状态指示器组件
 */
function MetadataStatusBadge({
  status,
  isMonitoring
}: {
  status: MetadataStatus | null;
  isMonitoring: boolean;
}) {
  if (!status) return null;

  const getStatusConfig = (status: MetadataStatus) => {
    switch (status) {
      case MetadataStatus.PENDING:
        return {
          icon: Clock,
          text: '待处理',
          className: 'bg-yellow-50 text-yellow-700 border-yellow-200'
        };
      case MetadataStatus.PROCESSING:
        return {
          icon: AlertCircle,
          text: isMonitoring ? '处理中...' : '处理中',
          className: 'bg-blue-50 text-blue-700 border-blue-200'
        };
      case MetadataStatus.COMPLETED:
        return {
          icon: CheckCircle,
          text: '已完成',
          className: 'bg-green-50 text-green-700 border-green-200'
        };
      case MetadataStatus.FAILED:
        return {
          icon: XCircle,
          text: '处理失败',
          className: 'bg-red-50 text-red-700 border-red-200'
        };
      default:
        return null;
    }
  };

  const config = getStatusConfig(status);
  if (!config) return null;

  const Icon = config.icon;

  return (
    <Badge variant="outline" className={config.className}>
      <Icon className="h-3 w-3 mr-1" />
      {config.text}
      {isMonitoring && (
        <div className="ml-1 w-2 h-2 bg-current rounded-full animate-pulse" />
      )}
    </Badge>
  );
}


