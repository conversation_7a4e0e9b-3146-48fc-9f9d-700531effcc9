import { NextRequest, NextResponse } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';
import {
  isValidFileType,
  isValidFileSize,
  getFileCategory,
  formatFileSize,
  generateUserId,
  DifyApiError
} from '~/lib/dify/types';

/**
 * 文件上传 API Route
 * POST /api/upload
 */
export async function POST(request: NextRequest) {
  try {
    // 解析 FormData
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const userId = formData.get('userId') as string || generateUserId();

    // 验证文件是否存在
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!isValidFileType(file)) {
      return NextResponse.json(
        { 
          error: 'Unsupported file type',
          details: `File type ${file.type} is not supported. Please upload images (JPEG, PNG, GIF, WebP) or documents (PDF, DOC, DOCX, etc.)`
        },
        { status: 400 }
      );
    }

    // 验证文件大小
    if (!isValidFileSize(file)) {
      const category = getFileCategory(file);
      const maxSize = category === 'image' ? '10MB' : '50MB';
      return NextResponse.json(
        { 
          error: 'File too large',
          details: `File size ${formatFileSize(file.size)} exceeds the maximum limit of ${maxSize} for ${category} files`
        },
        { status: 400 }
      );
    }

    // 获取 Dify 服务实例
    const difyServices = getDifyServices();

    // 注意：新架构中没有直接的 uploadFile 方法
    // 这个功能可能需要重新实现或者使用其他方式
    const uploadResult = { success: false, error: 'Upload not implemented in new architecture' };

    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: {
        id: uploadResult.id,
        name: uploadResult.name,
        size: uploadResult.size,
        type: getFileCategory(file),
        mimeType: uploadResult.mime_type,
        extension: uploadResult.extension,
        createdAt: uploadResult.created_at,
      }
    });

  } catch (error) {
    console.error('File upload error:', error);

    // 处理 Dify API 错误
    if (error instanceof DifyApiError) {
      return NextResponse.json(
        { 
          error: 'Upload service error',
          details: error.message,
          code: error.code 
        },
        { status: error.statusCode || 500 }
      );
    }

    // 处理其他错误
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: 'Failed to upload file. Please try again later.'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取支持的文件类型信息
 * GET /api/upload
 */
export async function GET() {
  return NextResponse.json({
    supportedTypes: {
      images: [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
      ],
      documents: [
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      ],
    },
    limits: {
      image: '10MB',
      document: '50MB',
    },
    tips: [
      'Images: JPEG, PNG, GIF, WebP, SVG (max 10MB)',
      'Documents: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT (max 50MB)',
      'For best results with homework questions, use clear, high-resolution images',
      'PDF files should contain text-based content for optimal processing',
    ]
  });
}
