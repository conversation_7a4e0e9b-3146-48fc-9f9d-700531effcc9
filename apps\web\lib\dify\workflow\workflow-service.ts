import { CompletionClient } from 'dify-client';

/**
 * WorkflowService - 处理 Dify 工作流程
 * 使用 CompletionClient 的 runWorkflow 方法处理工作流应用
 */
export class WorkflowService {
  private client: CompletionClient;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://api2.buyvs.com/v1') {
    this.baseUrl = baseUrl;
    this.client = new CompletionClient(apiKey, baseUrl);
  }

  /**
   * 运行工作流（流式）
   * @param inputs 工作流输入参数
   * @param userId 用户ID
   * @param files 可选的文件列表
   * @returns Promise<any> 流式响应
   */
  async runWorkflowStream(
    inputs: Record<string, any>,
    userId: string,
    files?: File[] | null
  ): Promise<any> {
    try {
      console.log('WorkflowService.runWorkflowStream called with:', {
        inputs,
        userId,
        files: files ? `${files.length} files` : 'no files'
      });

      const response = await this.client.runWorkflow(
        inputs,
        userId,
        true, // stream = true
        files || null
      );

      return response;

    } catch (error) {
      console.error('WorkflowService.runWorkflowStream error:', error);
      throw error;
    }
  }

  /**
   * 运行工作流（阻塞式）
   * @param inputs 工作流输入参数
   * @param userId 用户ID
   * @param files 可选的文件列表
   * @returns Promise<any> 完整响应
   */
  async runWorkflow(
    inputs: Record<string, any>,
    userId: string,
    files?: File[] | null
  ): Promise<any> {
    try {
      console.log('WorkflowService.runWorkflow called with:', {
        inputs,
        userId,
        files: files ? `${files.length} files` : 'no files'
      });

      const response = await this.client.runWorkflow(
        inputs,
        userId,
        false, // stream = false
        files || null
      );

      return response.data;

    } catch (error) {
      console.error('WorkflowService.runWorkflow error:', error);
      throw error;
    }
  }

  /**
   * 生成元数据 - 专门用于元数据工作流
   * @param conversationId Dify 对话ID
   * @param shortId 系统短ID
   * @param query 用户查询
   * @param answer 可选的回答内容
   * @returns Promise<any> 元数据生成结果
   */
  async generateMetadata(
    conversationId: string,
    shortId: string,
    query: string,
    answer?: string
  ): Promise<any> {
    try {
      console.log('WorkflowService.generateMetadata called with:', {
        conversationId,
        shortId,
        query: query.substring(0, 50) + '...',
        answer: answer ? answer.substring(0, 50) + '...' : 'no answer'
      });

      // 新的工作流设计：传入三个参数
      const inputs: Record<string, any> = {
        conversation_id: conversationId,  // Dify 对话ID，用于获取对话上下文
        short_id: shortId,               // 系统短ID，用于数据库操作
        text: query                      // 用户查询内容
      };

      // 如果有回答内容，也可以包含在输入中
      if (answer) {
        inputs.answer = answer;
      }

      const response = await this.runWorkflow(
        inputs,
        `metadata_${conversationId}`
      );

      console.log('Metadata generation successful:', response);
      return response;

    } catch (error) {
      console.error('WorkflowService.generateMetadata error:', error);
      throw error;
    }
  }

  /**
   * 更新 API 密钥
   * @param apiKey 新的 API 密钥
   */
  updateApiKey(apiKey: string): void {
    this.client.updateApiKey(apiKey);
  }
}
