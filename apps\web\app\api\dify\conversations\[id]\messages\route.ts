import { NextRequest, NextResponse } from 'next/server';
import { getDifyServices } from '~/lib/dify/dify-services';

/**
 * 获取 Dify 对话消息列表 API
 * GET /api/dify/conversations/[id]/messages
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id: conversationId } = await params;

    if (!conversationId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Conversation ID is required',
          code: 'MISSING_CONVERSATION_ID',
        },
        { status: 400 }
      );
    }

    const difyServices = getDifyServices();
    // 注意：新架构中没有直接的 getConversationMessages 方法
    // 这个端点可能需要重新设计或者使用其他方式获取消息
    const messages = [];

    return NextResponse.json({
      success: true,
      data: messages,
    });
  } catch (error) {
    console.error('Dify messages API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch conversation messages',
        details: error instanceof Error ? error.message : 'Unknown error',
        code: 'FETCH_MESSAGES_FAILED',
      },
      { status: 500 }
    );
  }
}
