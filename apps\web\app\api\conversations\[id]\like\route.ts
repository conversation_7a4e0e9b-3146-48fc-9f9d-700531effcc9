import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { ConversationService } from '~/lib/services/conversation-service';
import { ApiErrorResponse } from '~/lib/types/conversations';
import { isValidShortId } from '~/lib/utils/short-id';

/**
 * 点赞会话 API
 * POST /api/conversations/[id]/like
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;

    // 验证短 ID 格式
    if (!isValidShortId(id)) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Invalid conversation ID format',
        code: 'INVALID_ID',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 创建会话服务实例
    const supabase = getSupabaseServerAdminClient();
    const conversationService = new ConversationService(supabase);

    // 增加点赞数
    const success = await conversationService.incrementLikeCount(id);

    if (!success) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: 'Failed to like conversation or conversation not found',
        code: 'LIKE_FAILED',
      };
      return NextResponse.json(errorResponse, { status: 404 });
    }

    // 获取更新后的会话信息
    const conversation = await conversationService.getConversationByShortId(id);

    return NextResponse.json({
      success: true,
      data: conversation,
      message: 'Conversation liked successfully',
    });
  } catch (error) {
    console.error('Conversation like API error:', error);
    
    const errorResponse: ApiErrorResponse = {
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      code: 'INTERNAL_ERROR',
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
