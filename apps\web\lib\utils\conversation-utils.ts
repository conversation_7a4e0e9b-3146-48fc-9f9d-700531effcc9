/**
 * 会话相关的工具函数
 * 提供格式化、验证和转换等辅助功能
 */

import { ConversationIndexRow } from '../types/conversations';

/**
 * 学科分类映射
 */
export const CATEGORY_MAP = {
  1: { name: '数学', color: 'bg-blue-100 text-blue-800', icon: '📐' },
  2: { name: '物理', color: 'bg-purple-100 text-purple-800', icon: '⚛️' },
  3: { name: '化学', color: 'bg-green-100 text-green-800', icon: '🧪' },
  4: { name: '生物', color: 'bg-emerald-100 text-emerald-800', icon: '🧬' },
  5: { name: '历史', color: 'bg-amber-100 text-amber-800', icon: '📜' },
  6: { name: '语文', color: 'bg-red-100 text-red-800', icon: '📝' },
  7: { name: '英语', color: 'bg-indigo-100 text-indigo-800', icon: '🇬🇧' },
  8: { name: '地理', color: 'bg-teal-100 text-teal-800', icon: '🌍' },
  9: { name: '政治', color: 'bg-rose-100 text-rose-800', icon: '🏛️' },
  10: { name: '计算机', color: 'bg-gray-100 text-gray-800', icon: '💻' },
  99: { name: '其他', color: 'bg-slate-100 text-slate-800', icon: '📚' },
} as const;

/**
 * 获取学科分类信息
 */
export function getCategoryInfo(categoryId: number) {
  return CATEGORY_MAP[categoryId as keyof typeof CATEGORY_MAP] || CATEGORY_MAP[99];
}

/**
 * 格式化时间显示
 */
export function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}天前`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks}周前`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths}个月前`;
  }

  const diffInYears = Math.floor(diffInDays / 365);
  return `${diffInYears}年前`;
}

/**
 * 格式化数字显示（如浏览量、点赞数）
 */
export function formatNumber(num: number): string {
  if (num < 1000) {
    return num.toString();
  }

  if (num < 10000) {
    return `${(num / 1000).toFixed(1)}k`;
  }

  if (num < 1000000) {
    return `${Math.floor(num / 1000)}k`;
  }

  return `${(num / 1000000).toFixed(1)}M`;
}

/**
 * 生成会话的完整 URL
 */
export function getConversationUrl(conversation: ConversationIndexRow): string {
  return `/questions/${conversation.short_id}/${conversation.handle}`;
}

/**
 * 生成会话的分享 URL
 */
export function getShareUrl(conversation: ConversationIndexRow, baseUrl?: string): string {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');
  return `${base}${getConversationUrl(conversation)}`;
}

/**
 * 截断文本显示
 */
export function truncateText(text: string, maxLength: number = 100): string {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
}

/**
 * 验证会话数据
 */
export function validateConversationData(data: Partial<ConversationIndexRow>): string[] {
  const errors: string[] = [];

  if (!data.title || data.title.trim().length === 0) {
    errors.push('标题不能为空');
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push('描述不能为空');
  }

  if (!data.handle || data.handle.trim().length === 0) {
    errors.push('URL 标识符不能为空');
  }

  if (data.category_id && !CATEGORY_MAP[data.category_id as keyof typeof CATEGORY_MAP]) {
    errors.push('无效的学科分类');
  }

  return errors;
}

/**
 * 生成 SEO 友好的 handle
 */
export function generateHandle(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim()
    .slice(0, 50); // 限制长度
}

/**
 * 获取用户类型显示文本
 */
export function getUserTypeText(userType: number): string {
  switch (userType) {
    case 0:
      return '游客';
    case 1:
      return '注册用户';
    default:
      return '未知';
  }
}

/**
 * 检查是否为公开会话
 */
export function isPublicConversation(conversation: ConversationIndexRow): boolean {
  return conversation.is_public === true;
}

/**
 * 检查用户是否可以编辑会话
 */
export function canEditConversation(
  conversation: ConversationIndexRow,
  currentUserId?: string
): boolean {
  // 如果是注册用户创建的会话，只有创建者可以编辑
  if (conversation.user_type === 1) {
    return conversation.user_id === currentUserId;
  }
  
  // 游客创建的会话暂时不允许编辑
  return false;
}

/**
 * 获取会话状态标签
 */
export function getConversationStatusBadge(conversation: ConversationIndexRow) {
  if (conversation.is_public) {
    return {
      text: '公开',
      color: 'bg-green-100 text-green-800',
    };
  } else {
    return {
      text: '私有',
      color: 'bg-gray-100 text-gray-800',
    };
  }
}

/**
 * 排序选项
 */
export const SORT_OPTIONS = [
  { value: 'created_at', label: '创建时间', order: 'desc' },
  { value: 'updated_at', label: '更新时间', order: 'desc' },
  { value: 'view_count', label: '浏览量', order: 'desc' },
  { value: 'like_count', label: '点赞数', order: 'desc' },
] as const;

/**
 * 筛选选项
 */
export const FILTER_OPTIONS = {
  categories: Object.entries(CATEGORY_MAP).map(([id, info]) => ({
    value: parseInt(id),
    label: info.name,
    icon: info.icon,
  })),
  languages: [
    { value: 'zh', label: '中文' },
    { value: 'en', label: 'English' },
  ],
} as const;
