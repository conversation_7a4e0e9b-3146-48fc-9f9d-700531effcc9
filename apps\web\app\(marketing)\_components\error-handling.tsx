'use client';

import { useState } from 'react';
import { 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  Server, 
  FileX, 
  MessageCircleX,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

interface ErrorInfo {
  type: 'network' | 'server' | 'upload' | 'validation' | 'api' | 'unknown';
  message: string;
  details?: string;
  code?: string;
  retryable?: boolean;
}

interface ErrorDisplayProps {
  error: ErrorInfo;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  showDetails?: boolean;
}

export function ErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss, 
  className,
  showDetails = false 
}: ErrorDisplayProps) {
  const [detailsExpanded, setDetailsExpanded] = useState(showDetails);

  const getErrorIcon = (type: ErrorInfo['type']) => {
    switch (type) {
      case 'network':
        return Wifi;
      case 'server':
        return Server;
      case 'upload':
        return FileX;
      case 'api':
        return MessageCircleX;
      default:
        return AlertTriangle;
    }
  };

  const getErrorColor = (type: ErrorInfo['type']) => {
    switch (type) {
      case 'network':
        return 'text-orange-500';
      case 'server':
        return 'text-red-500';
      case 'upload':
        return 'text-blue-500';
      case 'api':
        return 'text-purple-500';
      default:
        return 'text-destructive';
    }
  };

  const getErrorTitle = (type: ErrorInfo['type']) => {
    switch (type) {
      case 'network':
        return 'Connection Error';
      case 'server':
        return 'Server Error';
      case 'upload':
        return 'Upload Error';
      case 'api':
        return 'AI Service Error';
      default:
        return 'Error';
    }
  };

  const Icon = getErrorIcon(error.type);

  return (
    <Alert variant="destructive" className={cn('w-full', className)}>
      <div className="flex items-start space-x-3">
        <Icon className={cn('w-5 h-5 mt-0.5', getErrorColor(error.type))} />
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center justify-between">
            <AlertTitle className="text-sm font-semibold">
              <Trans i18nKey={`error:${error.type}Title`} defaults={getErrorTitle(error.type)} />
            </AlertTitle>
            
            <div className="flex items-center space-x-2">
              {error.code && (
                <Badge variant="outline" className="text-xs">
                  {error.code}
                </Badge>
              )}
              
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDismiss}
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              )}
            </div>
          </div>

          <AlertDescription className="text-sm">
            {error.message}
          </AlertDescription>

          {/* 详细信息 */}
          {error.details && (
            <div className="space-y-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDetailsExpanded(!detailsExpanded)}
                className="h-6 px-0 text-xs text-muted-foreground hover:text-foreground"
              >
                {detailsExpanded ? (
                  <>
                    <ChevronUp className="w-3 h-3 mr-1" />
                    <Trans i18nKey="error:hideDetails" defaults="Hide details" />
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-3 h-3 mr-1" />
                    <Trans i18nKey="error:showDetails" defaults="Show details" />
                  </>
                )}
              </Button>
              
              {detailsExpanded && (
                <div className="bg-muted/50 p-3 rounded text-xs text-muted-foreground font-mono">
                  {error.details}
                </div>
              )}
            </div>
          )}

          {/* 重试按钮 */}
          {error.retryable && onRetry && (
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="h-8"
              >
                <RefreshCw className="w-3 h-3 mr-2" />
                <Trans i18nKey="error:retry" defaults="Try again" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </Alert>
  );
}

interface ErrorBoundaryCardProps {
  error: Error;
  onRetry?: () => void;
  className?: string;
}

export function ErrorBoundaryCard({ error, onRetry, className }: ErrorBoundaryCardProps) {
  return (
    <Card className={cn('w-full border-destructive', className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-destructive">
          <AlertTriangle className="w-5 h-5" />
          <span>
            <Trans i18nKey="error:somethingWentWrong" defaults="Something went wrong" />
          </span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          <Trans 
            i18nKey="error:unexpectedError" 
            defaults="An unexpected error occurred. Please try again or contact support if the problem persists." 
          />
        </p>
        
        <details className="text-xs">
          <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
            <Trans i18nKey="error:technicalDetails" defaults="Technical details" />
          </summary>
          <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto">
            {error.message}
            {error.stack && `\n\n${error.stack}`}
          </pre>
        </details>
        
        {onRetry && (
          <Button onClick={onRetry} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            <Trans i18nKey="error:retry" defaults="Try again" />
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

interface RetryButtonProps {
  onRetry: () => void;
  isRetrying?: boolean;
  retryCount?: number;
  maxRetries?: number;
  className?: string;
}

export function RetryButton({ 
  onRetry, 
  isRetrying = false, 
  retryCount = 0, 
  maxRetries = 3,
  className 
}: RetryButtonProps) {
  const canRetry = retryCount < maxRetries;

  return (
    <Button
      onClick={onRetry}
      disabled={!canRetry || isRetrying}
      variant="outline"
      size="sm"
      className={cn('h-8', className)}
    >
      <RefreshCw className={cn('w-3 h-3 mr-2', isRetrying && 'animate-spin')} />
      {isRetrying ? (
        <Trans i18nKey="error:retrying" defaults="Retrying..." />
      ) : canRetry ? (
        <Trans i18nKey="error:retry" defaults="Try again" />
      ) : (
        <Trans i18nKey="error:maxRetriesReached" defaults="Max retries reached" />
      )}
      {retryCount > 0 && (
        <Badge variant="secondary" className="ml-2 text-xs">
          {retryCount}/{maxRetries}
        </Badge>
      )}
    </Button>
  );
}

// 错误工具函数
export function createErrorInfo(
  error: unknown,
  context: string = 'unknown'
): ErrorInfo {
  if (error instanceof Error) {
    // 网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return {
        type: 'network',
        message: 'Unable to connect to the server. Please check your internet connection.',
        details: error.message,
        retryable: true,
      };
    }
    
    // API 错误
    if (error.message.includes('API') || error.message.includes('service')) {
      return {
        type: 'api',
        message: 'AI service is temporarily unavailable. Please try again.',
        details: error.message,
        retryable: true,
      };
    }
    
    // 上传错误
    if (context === 'upload') {
      return {
        type: 'upload',
        message: 'Failed to upload file. Please check the file format and size.',
        details: error.message,
        retryable: true,
      };
    }
    
    // 通用错误
    return {
      type: 'unknown',
      message: error.message || 'An unexpected error occurred.',
      details: error.stack,
      retryable: true,
    };
  }
  
  // 字符串错误
  if (typeof error === 'string') {
    return {
      type: 'unknown',
      message: error,
      retryable: true,
    };
  }
  
  // 未知错误
  return {
    type: 'unknown',
    message: 'An unknown error occurred.',
    details: String(error),
    retryable: true,
  };
}
