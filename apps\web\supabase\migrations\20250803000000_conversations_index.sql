-- 创建提问数据索引表
-- 用于存储对话的元数据和索引信息，实际内容从 Dify API 获取

-- 创建对话索引表
CREATE TABLE IF NOT EXISTS public.conversations_index (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Dify 对话 ID（来自 Dify API，初始为空，主工作流返回后填入）
  conversation_id TEXT UNIQUE,
  
  -- 短链接 ID（Base58 编码的 UUID）
  short_id TEXT NOT NULL UNIQUE,
  
  -- URL 友好的标识符（AI 生成，初始为空）
  handle TEXT,
  
  -- 问题标题（AI 生成，初始为空）
  title TEXT,
  
  -- 问题描述（AI 生成，初始为空）
  description TEXT,
  
  -- 语言标识
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  
  -- 学科分类 ID（AI 生成）
  -- 1=数学, 2=物理, 3=化学, 4=生物, 5=历史, 6=语文, 7=英语, 8=地理, 9=政治, 10=计算机, 99=其他
  category_id INTEGER NOT NULL DEFAULT 99,
  
  -- 用户 ID（如果是注册用户）
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- 可见性设置
  is_public BOOLEAN NOT NULL DEFAULT false,
  
  -- 用户类型：0=游客, 1=注册用户
  user_type SMALLINT NOT NULL DEFAULT 0 CHECK (user_type IN (0, 1)),
  
  -- 元数据处理状态：pending=等待处理, processing=处理中, completed=已完成, failed=处理失败
  metadata_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (metadata_status IN ('pending', 'processing', 'completed', 'failed')),
  
  -- 元数据处理重试次数：支持失败任务的自动重试机制
  metadata_retry_count INTEGER NOT NULL DEFAULT 0 CHECK (metadata_retry_count >= 0 AND metadata_retry_count <= 3),
  
  -- 客户端类型：web=网页版, android=安卓, ios=苹果, desktop=桌面应用, extension=浏览器插件, api=API调用
  client_type VARCHAR(20) NOT NULL DEFAULT 'web' CHECK (client_type IN ('web', 'android', 'ios', 'desktop', 'extension', 'api')),
  
  -- AI模型标识：记录使用的具体AI模型
  ai_model VARCHAR(50) NOT NULL,
  
  -- Dify API 对话用户标识符（主工作流返回后填入）
  conversation_user TEXT,
  
  -- 统计数据
  view_count INTEGER NOT NULL DEFAULT 0,
  like_count INTEGER NOT NULL DEFAULT 0,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_conversations_index_conversation_id ON public.conversations_index(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversations_index_short_id ON public.conversations_index(short_id);
CREATE INDEX IF NOT EXISTS idx_conversations_index_handle ON public.conversations_index(handle);
CREATE INDEX IF NOT EXISTS idx_conversations_index_user_id ON public.conversations_index(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_index_conversation_user ON public.conversations_index(conversation_user);
CREATE INDEX IF NOT EXISTS idx_conversations_index_public_category ON public.conversations_index(is_public, category_id);
CREATE INDEX IF NOT EXISTS idx_conversations_index_language ON public.conversations_index(language);
CREATE INDEX IF NOT EXISTS idx_conversations_index_metadata_status ON public.conversations_index(metadata_status);
CREATE INDEX IF NOT EXISTS idx_conversations_index_metadata_retry_count ON public.conversations_index(metadata_retry_count);
CREATE INDEX IF NOT EXISTS idx_conversations_index_client_type ON public.conversations_index(client_type);
CREATE INDEX IF NOT EXISTS idx_conversations_index_ai_model ON public.conversations_index(ai_model);
CREATE INDEX IF NOT EXISTS idx_conversations_index_created_at ON public.conversations_index(created_at DESC);

-- 创建复合索引用于社区页面查询
CREATE INDEX IF NOT EXISTS idx_conversations_index_public_list ON public.conversations_index(is_public, category_id, created_at DESC) WHERE is_public = true;

-- 创建复合索引用于 Dify API 查询
CREATE INDEX IF NOT EXISTS idx_conversations_index_conversation_user_composite ON public.conversations_index(conversation_id, conversation_user);

-- 添加表注释
COMMENT ON TABLE public.conversations_index IS '对话索引表：存储 Dify 对话的元数据和索引信息';
COMMENT ON COLUMN public.conversations_index.conversation_id IS 'Dify API 返回的对话 ID';
COMMENT ON COLUMN public.conversations_index.short_id IS '短链接 ID，通过算法编码 conversation_id 生成';
COMMENT ON COLUMN public.conversations_index.handle IS 'URL 友好的标识符，用于 SEO 友好的 URL';
COMMENT ON COLUMN public.conversations_index.category_id IS '学科分类 ID：1=数学, 2=物理, 3=化学, 4=生物, 5=历史, 6=语文, 7=英语, 8=地理, 9=政治, 10=计算机, 99=其他';
COMMENT ON COLUMN public.conversations_index.user_type IS '用户类型：0=游客用户, 1=注册用户';
COMMENT ON COLUMN public.conversations_index.metadata_status IS '元数据处理状态：pending=等待处理, processing=处理中, completed=已完成, failed=处理失败';
COMMENT ON COLUMN public.conversations_index.metadata_retry_count IS '元数据处理重试次数：支持失败任务的自动重试机制，范围 0-3，默认值 0';
COMMENT ON COLUMN public.conversations_index.client_type IS '客户端类型：web=网页版, android=安卓APP, ios=苹果APP, desktop=桌面应用, extension=浏览器插件, api=API调用';
COMMENT ON COLUMN public.conversations_index.ai_model IS 'AI模型标识：记录使用的具体AI模型，如 gpt-4, claude-3, gemini-pro 等';
COMMENT ON COLUMN public.conversations_index.conversation_user IS 'Dify API 返回的对话用户标识符，用于获取对话消息时的 user 参数';

-- 启用 RLS（行级安全）
ALTER TABLE public.conversations_index ENABLE ROW LEVEL SECURITY;

-- RLS 策略：用户可以查看公开的内容
CREATE POLICY "conversations_index_public_read" ON public.conversations_index
  FOR SELECT USING (is_public = true);

-- RLS 策略：注册用户可以查看自己的内容
CREATE POLICY "conversations_index_owner_read" ON public.conversations_index
  FOR SELECT USING (auth.uid() = user_id);

-- RLS 策略：注册用户可以插入自己的内容
CREATE POLICY "conversations_index_owner_insert" ON public.conversations_index
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_type = 0);

-- RLS 策略：注册用户可以更新自己的内容
CREATE POLICY "conversations_index_owner_update" ON public.conversations_index
  FOR UPDATE USING (auth.uid() = user_id);

-- 创建触发器自动更新 updated_at
CREATE OR REPLACE FUNCTION public.update_conversations_index_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER conversations_index_updated_at
  BEFORE UPDATE ON public.conversations_index
  FOR EACH ROW
  EXECUTE FUNCTION public.update_conversations_index_updated_at();

-- 授权
GRANT SELECT ON public.conversations_index TO authenticated, anon;
GRANT INSERT, UPDATE ON public.conversations_index TO authenticated;
