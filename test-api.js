// 使用内置的 fetch (Node.js 18+)

async function testChatAPI() {
  try {
    console.log('Testing Chat API...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'What is 2+2?'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      // 处理流式响应
      if (!response.body) {
        console.log('No response body');
        return;
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      console.log('Reading stream...');
      let chunks = [];

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          chunks.push(chunk);
          console.log('Chunk:', chunk);
        }

        console.log('Total chunks received:', chunks.length);
        console.log('Full response:', chunks.join(''));
      } catch (streamError) {
        console.error('Stream reading error:', streamError);
      }
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testChatAPI();
