{"name": "@kit/shared", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./logger": "./src/logger/index.ts", "./utils": "./src/utils.ts", "./hooks": "./src/hooks/index.ts", "./events": "./src/events/index.tsx", "./registry": "./src/registry/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/react": "19.1.8"}, "dependencies": {"pino": "^9.7.0"}, "typesVersions": {"*": {"*": ["src/*"]}}}