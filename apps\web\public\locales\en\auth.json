{"signUpHeading": "Create an account", "signUp": "Sign Up", "signUpSubheading": "Fill the form below to create an account.", "signInHeading": "Sign in to your account", "signInSubheading": "Welcome back! Please enter your details", "signIn": "Sign In", "getStarted": "Get started", "updatePassword": "Update Password", "signOut": "Sign out", "signingIn": "Signing in...", "signingUp": "Signing up...", "doNotHaveAccountYet": "Do not have an account yet?", "alreadyHaveAnAccount": "Already have an account?", "signUpToAcceptInvite": "Please sign in/up to accept the invite", "clickToAcceptAs": "Click the button below to accept the invite with as <b>{{email}}</b>", "acceptInvite": "Accept invite", "acceptingInvite": "Accepting Invite...", "acceptInviteSuccess": "<PERSON><PERSON><PERSON> successfully accepted", "acceptInviteError": "Error encountered while accepting invite", "acceptInviteWithDifferentAccount": "Want to accept the invite with a different account?", "alreadyHaveAccountStatement": "I already have an account, I want to sign in instead", "doNotHaveAccountStatement": "I do not have an account, I want to sign up instead", "signInWithProvider": "Sign in with {{provider}}", "signInWithPhoneNumber": "Sign in with Phone Number", "signInWithEmail": "Sign in with <PERSON><PERSON>", "signUpWithEmail": "Sign up with <PERSON><PERSON>", "passwordHint": "Ensure it's at least 8 characters", "repeatPasswordHint": "Type your password again", "repeatPassword": "Repeat password", "passwordForgottenQuestion": "Password forgotten?", "passwordResetLabel": "Reset Password", "passwordResetSubheading": "Enter your email address below. You will receive a link to reset your password.", "passwordResetSuccessMessage": "Check your Inbox! We emailed you a link for resetting your Password.", "passwordRecoveredQuestion": "Password recovered?", "passwordLengthError": "Please provide a password with at least 6 characters", "sendEmailLink": "Send Email Link", "sendingEmailLink": "Sending Email Link...", "sendLinkSuccessDescription": "Check your email, we just sent you a link. Follow the link to sign in.", "sendLinkSuccess": "We sent you a link by email", "sendLinkSuccessToast": "<PERSON> successfully sent", "getNewLink": "Get a new link", "verifyCodeHeading": "Verify your account", "verificationCode": "Verification Code", "verificationCodeHint": "Enter the code we sent you by SMS", "verificationCodeSubmitButtonLabel": "Submit Verification Code", "sendingMfaCode": "Sending Verification Code...", "verifyingMfaCode": "Verifying code...", "sendMfaCodeError": "Sorry, we couldn't send you a verification code", "verifyMfaCodeSuccess": "Code verified! Signing you in...", "verifyMfaCodeError": "Ops! It looks like the code is not correct", "reauthenticate": "Reauthenticate", "reauthenticateDescription": "For security reasons, we need you to re-authenticate", "errorAlertHeading": "Sorry, we could not authenticate you", "emailConfirmationAlertHeading": "We sent you a confirmation email.", "emailConfirmationAlertBody": "Welcome! Please check your email and click the link to verify your account.", "resendLink": "Resend link", "resendLinkSuccessDescription": "We sent you a new link to your email! Follow the link to sign in.", "resendLinkSuccess": "Check your email!", "authenticationErrorAlertHeading": "Authentication Error", "authenticationErrorAlertBody": "Sorry, we could not authenticate you. Please try again.", "sendEmailCode": "Get code to your Email", "sendingEmailCode": "Sending code...", "resetPasswordError": "Sorry, we could not reset your password. Please try again.", "emailPlaceholder": "<EMAIL>", "inviteAlertHeading": "You have been invited to join a team", "inviteAlertBody": "Please sign in or sign up to accept the invite and join the team.", "acceptTermsAndConditions": "I accept the <TermsOfServiceLink /> and <PrivacyPolicyLink />", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "orContinueWith": "Or continue with", "redirecting": "You're in! Please wait...", "lastUsedMethodPrefix": "You last signed in with", "methodPassword": "email and password", "methodOtp": "OTP code", "methodMagicLink": "email link", "methodOauth": "social sign-in", "methodOauthWithProvider": "<provider>{{provider}}</provider>", "methodDefault": "another method", "existingAccountHint": "You previously signed in with <method>{{method}}</method>. <signInLink>Already have an account?</signInLink>", "errors": {"Invalid login credentials": "The credentials entered are invalid", "User already registered": "This credential is already in use. Please try with another one.", "Email not confirmed": "Please confirm your email address before signing in", "default": "We have encountered an error. Please ensure you have a working internet connection and try again", "generic": "Sorry, we weren't able to authenticate you. Please try again.", "linkTitle": "Sign in failed", "linkDescription": "Sorry, we weren't able to sign you in. Please try again.", "codeVerifierMismatch": "It looks like you're trying to sign in using a different browser than the one you used to request the sign in link. Please try again using the same browser.", "minPasswordLength": "Password must be at least 8 characters long", "passwordsDoNotMatch": "The passwords do not match", "minPasswordNumbers": "Password must contain at least one number", "minPasswordSpecialChars": "Password must contain at least one special character", "Signups not allowed for otp": "OTP is disabled. Please enable it in your account settings.", "uppercasePassword": "Password must contain at least one uppercase letter", "insufficient_aal": "Please sign-in with your current multi-factor authentication to perform this action", "otp_expired": "The email link has expired. Please try again.", "same_password": "The password cannot be the same as the current password"}}