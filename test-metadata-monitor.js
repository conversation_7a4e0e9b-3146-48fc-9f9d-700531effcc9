async function testMetadataMonitor() {
  console.log('=== 元数据监控功能测试 ===');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. 测试获取监控概览
    console.log('\n1. 测试监控概览...');
    const overviewResponse = await fetch(`${baseUrl}/api/admin/metadata-monitor?action=overview`);
    
    if (overviewResponse.ok) {
      const overviewData = await overviewResponse.json();
      console.log('监控概览成功:', {
        failed_count: overviewData.data?.summary?.failed_count,
        processing_count: overviewData.data?.summary?.processing_count,
        stuck_count: overviewData.data?.summary?.stuck_count,
        total_issues: overviewData.data?.summary?.total_issues
      });
    } else {
      console.log('监控概览失败:', overviewResponse.status);
    }

    // 2. 测试获取失败任务
    console.log('\n2. 测试获取失败任务...');
    const failedResponse = await fetch(`${baseUrl}/api/admin/metadata-monitor?action=failed`);
    
    if (failedResponse.ok) {
      const failedData = await failedResponse.json();
      console.log('失败任务数量:', failedData.data?.count || 0);
      if (failedData.data?.tasks?.length > 0) {
        console.log('失败任务示例:', failedData.data.tasks[0]);
      }
    } else {
      console.log('获取失败任务失败:', failedResponse.status);
    }

    // 3. 测试获取处理中任务
    console.log('\n3. 测试获取处理中任务...');
    const processingResponse = await fetch(`${baseUrl}/api/admin/metadata-monitor?action=processing`);
    
    if (processingResponse.ok) {
      const processingData = await processingResponse.json();
      console.log('处理中任务数量:', processingData.data?.count || 0);
      if (processingData.data?.tasks?.length > 0) {
        console.log('处理中任务示例:', processingData.data.tasks[0]);
      }
    } else {
      console.log('获取处理中任务失败:', processingResponse.status);
    }

    // 4. 测试获取卡住任务
    console.log('\n4. 测试获取卡住任务...');
    const stuckResponse = await fetch(`${baseUrl}/api/admin/metadata-monitor?action=stuck`);
    
    if (stuckResponse.ok) {
      const stuckData = await stuckResponse.json();
      console.log('卡住任务数量:', stuckData.data?.count || 0);
      if (stuckData.data?.tasks?.length > 0) {
        console.log('卡住任务示例:', stuckData.data.tasks[0]);
      }
    } else {
      console.log('获取卡住任务失败:', stuckResponse.status);
    }

    // 5. 测试定时清理任务状态
    console.log('\n5. 测试定时清理任务状态...');
    const cronStatusResponse = await fetch(`${baseUrl}/api/cron/metadata-cleanup`);
    
    if (cronStatusResponse.ok) {
      const cronData = await cronStatusResponse.json();
      console.log('定时任务状态:', {
        failed_tasks: cronData.data?.status?.failed_tasks,
        processing_tasks: cronData.data?.status?.processing_tasks,
        stuck_tasks: cronData.data?.status?.stuck_tasks,
        needs_attention: cronData.data?.status?.needs_attention
      });
    } else {
      console.log('获取定时任务状态失败:', cronStatusResponse.status);
    }

    console.log('\n=== 元数据监控功能测试完成 ===');

  } catch (error) {
    console.error('测试失败:', error);
  }
}

testMetadataMonitor();
