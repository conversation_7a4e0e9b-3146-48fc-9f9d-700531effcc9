// 使用 Node.js 18+ 内置的 fetch

async function testMetadataAPI() {
  try {
    console.log('Testing Metadata API...');
    
    // 测试元数据处理
    const response = await fetch('http://localhost:3000/api/chat/metadata', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        conversation_id: 'LprCAtEf', // 使用最近创建的真实 conversation
        query: 'What is 2+2?',
        answer: '2 + 2 = 4\n\n这是一个简单的加法问题，把数字2和2相加，结果是4。如果你有其他数学题或者需要详细解释，随时告诉我！'
      }),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers));

    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testMetadataAPI();
