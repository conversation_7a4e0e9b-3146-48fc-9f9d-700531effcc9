---
title: "Authentication"
description: "Learn how to set up authentication in your MakerKit application."
publishedAt: 2024-04-11
order: 1
status: "published"
---

MakerKit uses Supabase to manage authentication within your application.

By default, every kit comes with the following built-in authentication methods:
- **Email/Password** - we added, by default, the traditional way of signing in
- **Third Party Providers** - we also added by default Google Auth sign-in
- **Email Links**
- **Phone Number**

You're free to add (or remove) any of the methods supported by Supabase's
Authentication: we will see how.

This documentation will help you with the following:
 - **Setup** - setting up your Supabase project
 - **SSR** - use SSR to persist your users' authentication, adding new
providers
 - **Customization** - an overview of how MakerKit works so that you can adapt
it to your own application's needs