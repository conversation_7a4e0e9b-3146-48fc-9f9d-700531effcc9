/**
 * React Query hooks for Dify integration
 * 提供 Dify 对话内容的数据获取和状态管理
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@kit/ui/sonner';

import {
  difyClient,
  isDifySuccess,
  isDifyError,
  DifyConversationDetail,
} from '../services/dify-client';
import { DifyMessage } from '../dify/types';

/**
 * Query Keys for Dify data
 */
export const difyKeys = {
  all: ['dify'] as const,
  conversations: () => [...difyKeys.all, 'conversations'] as const,
  conversation: (id: string) => [...difyKeys.conversations(), id] as const,
  messages: (id: string) => [...difyKeys.conversation(id), 'messages'] as const,
};

/**
 * Hook for fetching Dify conversation messages
 */
export function useDifyMessages(conversationId: string) {
  return useQuery({
    queryKey: difyKeys.messages(conversationId),
    queryFn: async () => {
      const response = await difyClient.getConversationMessages(conversationId);
      
      if (isDifyError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    enabled: !!conversationId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // 如果是 404 错误（对话不存在），不重试
      if (error.message.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

/**
 * Hook for fetching Dify conversation detail (including messages)
 */
export function useDifyConversation(conversationId: string) {
  return useQuery({
    queryKey: difyKeys.conversation(conversationId),
    queryFn: async () => {
      if (!conversationId) {
        throw new Error('Conversation ID is required');
      }

      const response = await difyClient.getConversationDetail(conversationId);

      if (isDifyError(response)) {
        throw new Error(response.error);
      }

      return response.data;
    },
    enabled: !!conversationId && conversationId.trim() !== '',
    staleTime: 5 * 60 * 1000, // 5 minutes - 增加缓存时间
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // 如果是 404 错误（对话不存在），不重试
      if (error.message.includes('not found') || error.message.includes('404')) {
        return false;
      }
      return failureCount < 2; // 减少重试次数
    },
    refetchOnWindowFocus: false, // 防止窗口聚焦时重新请求
    refetchOnMount: false, // 防止组件重新挂载时重新请求
  });
}

/**
 * Hook for sending message to Dify conversation
 */
export function useSendDifyMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      conversationId?: string;
      message: string;
      files?: Array<{
        type: 'image' | 'document';
        transfer_method: 'remote_url' | 'local_file';
        url?: string;
        upload_file_id?: string;
      }>;
    }) => {
      const response = await difyClient.sendMessage(params);
      
      if (isDifyError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    onSuccess: (data, variables) => {
      // 如果有 conversationId，更新对应的消息缓存
      if (variables.conversationId) {
        // 使消息列表缓存失效，触发重新获取
        queryClient.invalidateQueries({
          queryKey: difyKeys.messages(variables.conversationId),
        });
        
        // 使对话详情缓存失效
        queryClient.invalidateQueries({
          queryKey: difyKeys.conversation(variables.conversationId),
        });
      }
      
      toast.success('消息发送成功');
    },
    onError: (error: Error) => {
      toast.error(`发送失败: ${error.message}`);
    },
  });
}

/**
 * Hook for uploading file to Dify
 */
export function useUploadDifyFile() {
  return useMutation({
    mutationFn: async (file: File) => {
      const response = await difyClient.uploadFile(file);
      
      if (isDifyError(response)) {
        throw new Error(response.error);
      }
      
      return response.data;
    },
    onSuccess: () => {
      toast.success('文件上传成功');
    },
    onError: (error: Error) => {
      toast.error(`上传失败: ${error.message}`);
    },
  });
}

/**
 * Hook for prefetching Dify conversation data
 * 用于在用户可能访问对话详情页面之前预加载数据
 */
export function usePrefetchDifyConversation() {
  const queryClient = useQueryClient();

  return (conversationId: string) => {
    queryClient.prefetchQuery({
      queryKey: difyKeys.conversation(conversationId),
      queryFn: async () => {
        const response = await difyClient.getConversationDetail(conversationId);
        
        if (isDifyError(response)) {
          throw new Error(response.error);
        }
        
        return response.data;
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    });
  };
}

/**
 * 格式化 Dify 消息的辅助函数
 */
export function formatDifyMessage(message: DifyMessage) {
  return {
    id: message.id,
    role: message.query ? 'user' : 'assistant',
    content: message.answer || message.query || '',
    createdAt: new Date(message.created_at * 1000),
    files: message.message_files || [],
    metadata: {
      conversationId: message.conversation_id,
      messageId: message.id,
      feedback: message.feedback,
    },
  };
}

/**
 * 检查 Dify 消息是否为用户消息
 */
export function isDifyUserMessage(message: DifyMessage): boolean {
  return !!message.query;
}

/**
 * 检查 Dify 消息是否为助手回复
 */
export function isDifyAssistantMessage(message: DifyMessage): boolean {
  return !!message.answer;
}

/**
 * 获取 Dify 消息的显示内容
 */
export function getDifyMessageContent(message: DifyMessage): string {
  // 如果消息同时包含 query 和 answer，优先显示 answer（AI 回答）
  if (message.answer) {
    return message.answer;
  }

  if (message.query) {
    return message.query;
  }

  return '';
}
